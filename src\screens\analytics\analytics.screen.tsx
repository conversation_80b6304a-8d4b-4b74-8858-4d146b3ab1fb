import { FunctionComponent, useContext, useEffect, useState } from "react";
import PageProps from "../../models/PageProps.interface";

//Widgets
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import { Divider, Typography } from "@mui/material";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";
import { useDispatch, useSelector } from "react-redux";

//Css Import
import "../analytics/analytics.screen.style.css";
import HomeChartCard from "../../components/homeChartCard/homeChartCard.component";
import RevenueChartDashboard from "../../components/revenueChartDashboard/revenueChartDashboard.component";
import DateFilter from "../../components/dateFilter/dateFilter.component";
import PlatformBreakdownChart from "../dashboardV2/platformBreakdownChart";
import LocationMetricsService from "../../services/locationMetrics/locationMetrics.service";
import { ILocationMetricsRequestModel } from "../../interfaces/request/ILocationMetricsRequestModel";
import dayjs from "dayjs";
import WebsiteClicksChart from "../dashboardV2/websiteClicksChart";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import ApplicationHelperService from "../../services/ApplicationHelperService";
import BusinessProfileInteractionsChart from "../dashboardV2/businessProfileInteractionsChart";
import BusinessService from "../../services/business/business.service";
import {
  ILocation,
  ILocationsListResponseModel,
} from "../../interfaces/response/ILocationsListResponseModel";
import { LoadingContext } from "../../context/loading.context";

interface EventCounts {
  type: string;
  total: number;
}

interface IGraphDataModel {
  data: number[];
  labels: string[];
}

interface IAggregatedAnalyticsData {
  multiDailyMetricTimeSeries: Array<{
    dailyMetricTimeSeries: any[];
  }>;
  totalLocations: number;
  processedLocations: number;
}

const Analytics: FunctionComponent<PageProps> = () => {
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const [locationList, setLocationList] = useState<ILocation[]>([]);
  const { setLoading } = useContext(LoadingContext);
  const _businessService = new BusinessService(dispatch);
  const _locationMetricsService = new LocationMetricsService(dispatch);
  const _applicationHelperService = new ApplicationHelperService(dispatch);
  const { setToastConfig } = useContext(ToastContext);

  const [selectedDateRange, setSelectedDateRange] = useState<{
    from: string;
    to: string;
    isSameMonthYear: boolean;
  } | null>(null);

  const [aggregatedAnalyticsData, setAggregatedAnalyticsData] =
    useState<IAggregatedAnalyticsData | null>(null);

  const [count1, setCount1] = useState<EventCounts>({
    type: "Calls",
    total: 0,
  });
  const [count2, setCount2] = useState<EventCounts>({
    type: "Directions",
    total: 0,
  });
  const [count3, setCount3] = useState<EventCounts>({
    type: "Website clicks",
    total: 0,
  });

  const INITIAL_GRAPH_DATA: IGraphDataModel = { data: [], labels: [] };

  const [websiteData, setWebsiteData] =
    useState<IGraphDataModel>(INITIAL_GRAPH_DATA);
  const [callData, setCallData] = useState<IGraphDataModel>(INITIAL_GRAPH_DATA);
  const [directionsData, setDirectionsData] =
    useState<IGraphDataModel>(INITIAL_GRAPH_DATA);
  const [messagingClicks, setMessagingClicks] =
    useState<IGraphDataModel>(INITIAL_GRAPH_DATA);
  const [bookings, setBookings] = useState<IGraphDataModel>(INITIAL_GRAPH_DATA);

  useEffect(() => {
    fetchLocations();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (locationList.length > 0 && selectedDateRange) {
      fetchAggregatedAnalyticsData(
        selectedDateRange.from,
        selectedDateRange.to,
        selectedDateRange.isSameMonthYear
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [locationList, selectedDateRange]);

  const VALIDATION_DAYS: number = 15;

  // Function to fetch all locations for the user
  const fetchLocations = async () => {
    try {
      setLoading(true);
      const locationListResponse: ILocationsListResponseModel =
        await _businessService.getLocations(userInfo.id);
      console.log("Location List: ", locationListResponse.list);
      setLocationList(locationListResponse.list);
    } catch (error) {
      console.error("Failed to fetch locations", error);
      setToastConfig(ToastSeverity.Error, "Failed to fetch locations", true);
    } finally {
      setLoading(false);
    }
  };

  // Function to aggregate metrics from all locations
  const aggregateMetricData = (
    allLocationData: any[],
    metric: string,
    daysDifference: number,
    isSameMonthYear: boolean
  ) => {
    const aggregatedDaily: Record<string, number> = {};
    const aggregatedMonthly: Record<string, number> = {};

    // Process each location's data
    allLocationData.forEach((locationData) => {
      if (!locationData || !locationData.multiDailyMetricTimeSeries) return;

      // Handle the nested structure properly
      locationData.multiDailyMetricTimeSeries.forEach((multiMetric: any) => {
        if (!multiMetric.dailyMetricTimeSeries) return;

        const metricSeries = multiMetric.dailyMetricTimeSeries.find(
          (series: any) => series.dailyMetric === metric
        );

        if (
          !metricSeries ||
          !metricSeries.timeSeries ||
          !metricSeries.timeSeries.datedValues
        )
          return;

        // Aggregate values for this location
        for (const entry of metricSeries.timeSeries.datedValues) {
          if (!entry.date) continue;

          const { year, month, day } = entry.date;
          const key =
            daysDifference < VALIDATION_DAYS || isSameMonthYear
              ? dayjs(`${year}-${month}-${day}`).format("YYYY-MMM-DD")
              : dayjs(`${year}-${month}-01`).format("MMM YYYY");

          const value = parseInt(entry.value ?? "0", 10);
          if (daysDifference < VALIDATION_DAYS || isSameMonthYear) {
            aggregatedDaily[key] = (aggregatedDaily[key] || 0) + value;
          } else {
            aggregatedMonthly[key] = (aggregatedMonthly[key] || 0) + value;
          }
        }
      });
    });

    const labels =
      daysDifference < VALIDATION_DAYS || isSameMonthYear
        ? Object.keys(aggregatedDaily).sort()
        : Object.keys(aggregatedMonthly);
    const data =
      daysDifference < VALIDATION_DAYS || isSameMonthYear
        ? labels.map((label) => aggregatedDaily[label] || 0)
        : labels.map((label) => aggregatedMonthly[label] || 0);

    return { data, labels };
  };

  // Function to fetch and aggregate analytics data from all locations
  const fetchAggregatedAnalyticsData = async (
    from: string,
    to: string,
    isSameMonthYear: boolean
  ) => {
    const daysDifference: number = _applicationHelperService.getDaysDifference(
      from,
      to
    );

    try {
      setLoading(true);
      const requestObj: ILocationMetricsRequestModel = {
        startDate: from,
        endDate: to,
      };

      // Fetch data for all locations
      const allLocationPromises = locationList.map(async (location) => {
        try {
          const getMetricsRequestHeader = {
            "x-gmb-account-id": location.gmbAccountId,
            "x-gmb-location-id": location.gmbLocationId,
          };

          const response = await _locationMetricsService.getLocationMetrics(
            requestObj,
            getMetricsRequestHeader
          );
          return response.data;
        } catch (error) {
          console.error(
            `Failed to fetch data for location ${location.gmbLocationName}:`,
            error
          );
          return null;
        }
      });

      const allLocationData = await Promise.all(allLocationPromises);
      const validLocationData = allLocationData.filter((data) => data !== null);

      console.log("Aggregated Analytics Data:", validLocationData);

      // Create aggregated analytics data structure that matches the expected format
      const aggregatedMultiDailyMetricTimeSeries: Array<{
        dailyMetricTimeSeries: any[];
      }> =
        validLocationData.length > 0
          ? [
              {
                dailyMetricTimeSeries: [],
              },
            ]
          : [];

      // Flatten all dailyMetricTimeSeries from all locations
      const allDailyMetricTimeSeries: any[] = [];
      validLocationData.forEach((locationData) => {
        if (locationData && locationData.multiDailyMetricTimeSeries) {
          locationData.multiDailyMetricTimeSeries.forEach(
            (multiMetric: any) => {
              if (multiMetric.dailyMetricTimeSeries) {
                allDailyMetricTimeSeries.push(
                  ...multiMetric.dailyMetricTimeSeries
                );
              }
            }
          );
        }
      });

      // Group by metric type and aggregate the datedValues
      const groupedMetrics: Record<string, any> = {};
      allDailyMetricTimeSeries.forEach((metric) => {
        if (!groupedMetrics[metric.dailyMetric]) {
          groupedMetrics[metric.dailyMetric] = {
            dailyMetric: metric.dailyMetric,
            timeSeries: {
              datedValues: [],
            },
          };
        }

        if (metric.timeSeries && metric.timeSeries.datedValues) {
          // Instead of pushing all values, we need to aggregate them by date
          metric.timeSeries.datedValues.forEach((datedValue: any) => {
            const existingValue = groupedMetrics[
              metric.dailyMetric
            ].timeSeries.datedValues.find(
              (existing: any) =>
                existing.date.year === datedValue.date.year &&
                existing.date.month === datedValue.date.month &&
                existing.date.day === datedValue.date.day
            );

            if (existingValue) {
              // Aggregate the values for the same date
              const currentValue = parseInt(existingValue.value || "0", 10);
              const newValue = parseInt(datedValue.value || "0", 10);
              existingValue.value = (currentValue + newValue).toString();
            } else {
              // Add new date entry
              groupedMetrics[metric.dailyMetric].timeSeries.datedValues.push({
                date: { ...datedValue.date },
                value: datedValue.value || "0",
              });
            }
          });
        }
      });

      // Convert back to array format and ensure we have data
      if (aggregatedMultiDailyMetricTimeSeries.length > 0) {
        aggregatedMultiDailyMetricTimeSeries[0].dailyMetricTimeSeries =
          Object.values(groupedMetrics);
      }

      const aggregatedData: IAggregatedAnalyticsData = {
        multiDailyMetricTimeSeries: aggregatedMultiDailyMetricTimeSeries,
        totalLocations: locationList.length,
        processedLocations: validLocationData.length,
      };

      setAggregatedAnalyticsData(aggregatedData);

      // Debug logging
      console.log("Final aggregated data structure:", aggregatedData);
      if (aggregatedData.multiDailyMetricTimeSeries.length > 0) {
        console.log(
          "First element dailyMetricTimeSeries:",
          aggregatedData.multiDailyMetricTimeSeries[0].dailyMetricTimeSeries
        );
      }

      // Aggregate different metrics
      const webSiteClicksData = aggregateMetricData(
        validLocationData,
        "WEBSITE_CLICKS",
        daysDifference,
        isSameMonthYear
      );
      setWebsiteData(webSiteClicksData);
      handleDataFromChild(webSiteClicksData, "Website clicks");

      const callData = aggregateMetricData(
        validLocationData,
        "CALL_CLICKS",
        daysDifference,
        isSameMonthYear
      );
      setCallData(callData);
      handleDataFromChild(callData, "Calls");

      const directionsData = aggregateMetricData(
        validLocationData,
        "BUSINESS_DIRECTION_REQUESTS",
        daysDifference,
        isSameMonthYear
      );
      setDirectionsData(directionsData);
      handleDataFromChild(directionsData, "Directions");

      setMessagingClicks(
        aggregateMetricData(
          validLocationData,
          "BUSINESS_CONVERSATIONS",
          daysDifference,
          isSameMonthYear
        )
      );

      setBookings(
        aggregateMetricData(
          validLocationData,
          "BUSINESS_FOOD_ORDERS",
          daysDifference,
          isSameMonthYear
        )
      );

      if (validLocationData.length < locationList.length) {
        setToastConfig(
          ToastSeverity.Warning,
          `Data fetched for ${validLocationData.length} out of ${locationList.length} locations`,
          true
        );
      }
    } catch (error: any) {
      console.error("Failed to fetch aggregated analytics data", error);
      setWebsiteData(INITIAL_GRAPH_DATA);
      setCallData(INITIAL_GRAPH_DATA);
      setDirectionsData(INITIAL_GRAPH_DATA);
      setMessagingClicks(INITIAL_GRAPH_DATA);
      setBookings(INITIAL_GRAPH_DATA);
      setCount1({ ...count1, total: 0 });
      setCount2({ ...count2, total: 0 });
      setCount3({ ...count3, total: 0 });
      setAggregatedAnalyticsData(null);

      if (error.response?.data?.error) {
        setToastConfig(ToastSeverity.Error, error.response.data.error, true);
      } else if (error.response?.data?.message) {
        setToastConfig(ToastSeverity.Error, error.response.data.message, true);
      } else {
        setToastConfig(
          ToastSeverity.Error,
          "Failed to fetch analytics data",
          true
        );
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDataFromChild = (data: any, type: string) => {
    console.log("Aggregated data from child:", data, type);
    const total =
      data &&
      data.data &&
      data.data.reduce((sum: number, val: number) => sum + val, 0);
    if (type === "Calls") {
      setCount1({
        ...count1,
        total: total,
      });
    } else if (type === "Directions") {
      setCount2({ ...count2, total: total });
    } else if (type === "Website clicks") {
      setCount3({ ...count3, total: total });
    }
  };

  return (
    <div>
      <Box>
        <Box>
          <LeftMenuComponent>
            <Box
              sx={{
                pr: 1,
              }}
            >
              <Box>
                <Box>
                  <Box sx={{ marginBottom: "5px" }}>
                    <h3 className="pageTitle">Analytics - All Locations</h3>
                    <Typography variant="subtitle2" className="subtitle2">
                      Hi, {userInfo && userInfo.name}. Analytics data aggregated
                      from all your locations.
                    </Typography>
                    {aggregatedAnalyticsData && (
                      <Typography
                        variant="body2"
                        sx={{ mt: 1, color: "text.secondary" }}
                      >
                        Showing data from{" "}
                        {aggregatedAnalyticsData.processedLocations} out of{" "}
                        {aggregatedAnalyticsData.totalLocations} locations
                      </Typography>
                    )}
                  </Box>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6} lg={9}>
                      <Typography variant="h6" sx={{ mb: 1 }}>
                        All Locations Analytics
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Data aggregated from {locationList.length} location(s)
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6} lg={3}>
                      <DateFilter
                        onDateChange={(range: any) => {
                          setSelectedDateRange(range);
                        }}
                      />
                    </Grid>
                  </Grid>
                  <Divider style={{ margin: "10px 0", height: 0 }} />
                  <Box>
                    <Grid
                      container
                      spacing={2}
                      className="commonCardBottomSpacing"
                    >
                      <Grid item xs={12} md={4} lg={4}>
                        <Box className="commonCard dashboardTopIconCard">
                          <Box className="dashboardTopIcon">
                            <img
                              alt="MyLocoBiz - Logo"
                              className="innerImage1"
                              src={require("../../assets/dashboard/Picture2.png")}
                            />
                          </Box>
                          <Box className="dashboardTopInfo">
                            <Typography className="dashboardTopCount">
                              {count1.total}
                            </Typography>
                            <Typography className="dashboardTopTitle">
                              {count1.type} (All Locations)
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                      <Grid item xs={12} md={4} lg={4}>
                        <Box className="commonCard dashboardTopIconCard">
                          <Box className="dashboardTopIcon">
                            <img
                              alt="MyLocoBiz - Logo"
                              className="innerImage2"
                              src={require("../../assets/dashboard/Picture1.png")}
                            />
                          </Box>
                          <Box className="dashboardTopInfo">
                            <Typography className="dashboardTopCount">
                              {count2.total}
                            </Typography>
                            <Typography className="dashboardTopTitle">
                              {count2.type} (All Locations)
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                      <Grid item xs={12} md={4} lg={4}>
                        <Box className="commonCard dashboardTopIconCard">
                          <Box className="dashboardTopIcon">
                            <img
                              alt="MyLocoBiz - Logo"
                              className="innerImage3"
                              src={require("../../assets/dashboard/Picture3.png")}
                            />
                          </Box>
                          <Box className="dashboardTopInfo">
                            <Typography className="dashboardTopCount">
                              {count3.total}
                            </Typography>
                            <Typography className="dashboardTopTitle">
                              {count3.type} (All Locations)
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    </Grid>

                    <Grid
                      container
                      spacing={2}
                      className="commonCardBottomSpacing"
                    >
                      {/* Temporarily disabled BusinessProfileInteractionsChart due to data structure compatibility */}
                      {false &&
                        aggregatedAnalyticsData &&
                        aggregatedAnalyticsData.multiDailyMetricTimeSeries &&
                        aggregatedAnalyticsData.multiDailyMetricTimeSeries
                          .length > 0 &&
                        aggregatedAnalyticsData.multiDailyMetricTimeSeries[0] &&
                        aggregatedAnalyticsData.multiDailyMetricTimeSeries[0]
                          .dailyMetricTimeSeries &&
                        aggregatedAnalyticsData.multiDailyMetricTimeSeries[0]
                          .dailyMetricTimeSeries.length > 0 && (
                          <Grid item xs={12} md={12}>
                            <BusinessProfileInteractionsChart
                              rawData={{
                                multiDailyMetricTimeSeries:
                                  aggregatedAnalyticsData.multiDailyMetricTimeSeries,
                              }}
                              isSameMonthYear={
                                selectedDateRange?.isSameMonthYear
                                  ? true
                                  : false
                              }
                            />
                          </Grid>
                        )}

                      {/* Show a placeholder message for now */}
                      <Grid item xs={12} md={12}>
                        <Box
                          className="commonCard"
                          sx={{ p: 3, textAlign: "center" }}
                        >
                          <Typography variant="h6" gutterBottom>
                            Business Profile Interactions (Aggregated)
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Aggregated interactions data from{" "}
                            {locationList.length} location(s)
                          </Typography>
                          <Typography
                            variant="h4"
                            sx={{ mt: 2, color: "primary.main" }}
                          >
                            {(
                              count1.total +
                              count2.total +
                              count3.total
                            ).toLocaleString()}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Total interactions (Calls + Directions + Website
                            Clicks)
                          </Typography>
                        </Box>
                      </Grid>

                      <Grid item xs={12} md={6} lg={6}>
                        <Box className="commonCard height100 pieChartDiv">
                          <Box
                            sx={{
                              p: 2,
                              backgroundColor: "#fff",
                              borderRadius: 2,
                            }}
                          >
                            <PlatformBreakdownChart
                              data={{
                                multiDailyMetricTimeSeries:
                                  aggregatedAnalyticsData?.multiDailyMetricTimeSeries ||
                                  [],
                              }}
                            />
                          </Box>
                        </Box>
                      </Grid>
                      <Grid item xs={12} md={6} lg={6}>
                        <RevenueChartDashboard
                          data1={callData.data}
                          data2={directionsData.data}
                          labels={directionsData.labels}
                          title1="Call Data (All Locations)"
                          title2="Directions Data (All Locations)"
                          graphTitle="Directions Vs Calls - Aggregated"
                        />
                      </Grid>
                    </Grid>
                  </Box>
                  <Box>
                    <Grid
                      container
                      spacing={2}
                      className="commonCardBottomSpacing"
                    >
                      <Grid item xs={12} md={6}>
                        <WebsiteClicksChart
                          {...callData}
                          title="Call clicks made from all Business Profiles"
                          graphTitle="Calls (Aggregated)"
                          colorCode={0}
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <WebsiteClicksChart
                          {...messagingClicks}
                          title="Messaging clicks made from all Business Profiles"
                          graphTitle="Messaging clicks (Aggregated)"
                          colorCode={1}
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <WebsiteClicksChart
                          {...bookings}
                          title="Bookings made from all Business Profiles"
                          graphTitle="Bookings (Aggregated)"
                          colorCode={0}
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <WebsiteClicksChart
                          {...directionsData}
                          title="Direction requests made from all Business Profiles"
                          graphTitle="Directions (Aggregated)"
                          colorCode={1}
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <WebsiteClicksChart
                          {...websiteData}
                          title="Website clicks made from all Business Profiles"
                          graphTitle="Website clicks (Aggregated)"
                          colorCode={0}
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <Box className="commonCard height100 pieChartDiv">
                          <HomeChartCard />
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                </Box>
              </Box>
            </Box>
          </LeftMenuComponent>
        </Box>
      </Box>
    </div>
  );
};

export default Analytics;
