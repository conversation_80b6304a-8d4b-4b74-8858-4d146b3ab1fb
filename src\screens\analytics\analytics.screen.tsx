import { FunctionComponent, useContext, useEffect, useState } from "react";
import PageProps from "../../models/PageProps.interface";

//Widgets
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import { Divider, Typography } from "@mui/material";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";
import { useDispatch, useSelector } from "react-redux";

//Css Import
import "../analytics/analytics.screen.style.css";
import HomeChartCard from "../../components/homeChartCard/homeChartCard.component";
import RevenueChartDashboard from "../../components/revenueChartDashboard/revenueChartDashboard.component";
import DateFilter from "../../components/dateFilter/dateFilter.component";
import PlatformBreakdownChart from "../dashboardV2/platformBreakdownChart";
import LocationMetricsService from "../../services/locationMetrics/locationMetrics.service";
import { ILocationMetricsRequestModel } from "../../interfaces/request/ILocationMetricsRequestModel";
import dayjs from "dayjs";
import WebsiteClicksChart from "../dashboardV2/websiteClicksChart";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import ApplicationHelperService from "../../services/ApplicationHelperService";
import BusinessService from "../../services/business/business.service";
import {
  ILocation,
  ILocationsListResponseModel,
} from "../../interfaces/response/ILocationsListResponseModel";
import {
  IBusiness,
  IBusinessListResponseModel,
} from "../../interfaces/response/IBusinessListResponseModel";
import {
  IBusinessGroup,
  IBusinessGroupsResponseModel,
} from "../../interfaces/response/IBusinessGroupsResponseModel";
import { Formik, getIn } from "formik";
import * as yup from "yup";
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  SelectChangeEvent,
  OutlinedInput,
  Chip,
  Button,
  Checkbox,
  ListItemText,
} from "@mui/material";
import { LoadingContext } from "../../context/loading.context";

interface EventCounts {
  type: string;
  total: number;
}

interface IGraphDataModel {
  data: number[];
  labels: string[];
}

interface IAggregatedAnalyticsData {
  multiDailyMetricTimeSeries: Array<{
    dailyMetricTimeSeries: any[];
  }>;
  totalLocations: number;
  processedLocations: number;
}

interface IAnalyticsFilterModel {
  businessId: number;
  businessGroupId: string;
  locationIds: string[];
}

const Analytics: FunctionComponent<PageProps> = () => {
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state: any) => state.authReducer);
  // Dropdown data states
  const [businessList, setBusinessList] = useState<IBusiness[]>([]);
  const [businessGroups, setBusinessGroups] = useState<IBusinessGroup[]>([]);
  const [locationList, setLocationList] = useState<ILocation[]>([]);

  const { setLoading } = useContext(LoadingContext);
  const _businessService = new BusinessService(dispatch);
  const _locationMetricsService = new LocationMetricsService(dispatch);
  const _applicationHelperService = new ApplicationHelperService(dispatch);
  const { setToastConfig } = useContext(ToastContext);

  // Form initial values
  const INITIAL_VALUES: IAnalyticsFilterModel = {
    businessId: 0,
    businessGroupId: "0",
    locationIds: [],
  };

  const [selectedDateRange, setSelectedDateRange] = useState<{
    from: string;
    to: string;
    isSameMonthYear: boolean;
  } | null>(null);

  const [aggregatedAnalyticsData, setAggregatedAnalyticsData] =
    useState<IAggregatedAnalyticsData | null>(null);

  const [count1, setCount1] = useState<EventCounts>({
    type: "Calls",
    total: 0,
  });
  const [count2, setCount2] = useState<EventCounts>({
    type: "Directions",
    total: 0,
  });
  const [count3, setCount3] = useState<EventCounts>({
    type: "Website clicks",
    total: 0,
  });

  const INITIAL_GRAPH_DATA: IGraphDataModel = { data: [], labels: [] };

  const [websiteData, setWebsiteData] =
    useState<IGraphDataModel>(INITIAL_GRAPH_DATA);
  const [callData, setCallData] = useState<IGraphDataModel>(INITIAL_GRAPH_DATA);
  const [directionsData, setDirectionsData] =
    useState<IGraphDataModel>(INITIAL_GRAPH_DATA);
  const [messagingClicks, setMessagingClicks] =
    useState<IGraphDataModel>(INITIAL_GRAPH_DATA);
  const [bookings, setBookings] = useState<IGraphDataModel>(INITIAL_GRAPH_DATA);

  useEffect(() => {
    getBusiness();
    getBusinessGroups();
    fetchLocations();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const VALIDATION_DAYS: number = 15;

  // MenuProps for multi-select dropdown
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: 300,
        width: "auto", // Allow auto width to show full content
        minWidth: 300, // Minimum width for better readability
      },
    },
    getContentAnchorEl: null,
    anchorOrigin: {
      vertical: "bottom" as const,
      horizontal: "left" as const,
    },
    transformOrigin: {
      vertical: "top" as const,
      horizontal: "left" as const,
    },
  };

  // Handle form submission
  const handleFormSubmit = (values: IAnalyticsFilterModel) => {
    if (selectedDateRange && values.locationIds.length > 0) {
      fetchAggregatedAnalyticsData(
        selectedDateRange.from,
        selectedDateRange.to,
        selectedDateRange.isSameMonthYear,
        values.locationIds.filter((id) => id !== "select-all")
      );
    } else if (!selectedDateRange) {
      setToastConfig(ToastSeverity.Warning, "Please select a date range", true);
    }
  };

  // Validation schema
  const AnalyticsSchema = yup.object().shape({
    businessId: yup
      .number()
      .typeError("Business ID must be a number")
      .moreThan(0, "Business must be selected")
      .required("Business is required"),
    businessGroupId: yup
      .string()
      .nonNullable()
      .required("Account is required")
      .test("len", `Account is required`, (val) => val != "0"),
    locationIds: yup
      .array()
      .of(yup.string())
      .min(1, "At least one location must be selected")
      .required("Locations are required"),
  });

  // Function to fetch businesses
  const getBusiness = async () => {
    try {
      setLoading(true);
      const roles: IBusinessListResponseModel =
        await _businessService.getBusiness(userInfo.id);
      if (roles.list.length > 0) {
        setBusinessList(roles.list);
      }
    } catch (error) {
      console.error("Failed to fetch businesses", error);
      setToastConfig(ToastSeverity.Error, "Failed to fetch businesses", true);
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch business groups
  const getBusinessGroups = async () => {
    try {
      setLoading(true);
      const businessGroups: IBusinessGroupsResponseModel =
        await _businessService.getBusinessGroups(userInfo.id);
      if (businessGroups.data.length > 0) {
        setBusinessGroups(businessGroups.data);
      }
    } catch (error) {
      console.error("Failed to fetch business groups", error);
      setToastConfig(
        ToastSeverity.Error,
        "Failed to fetch business groups",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch all locations for the user
  const fetchLocations = async () => {
    try {
      setLoading(true);
      const locationListResponse: ILocationsListResponseModel =
        await _businessService.getLocations(userInfo.id);
      console.log("Location List: ", locationListResponse.list);
      setLocationList(locationListResponse.list);
    } catch (error) {
      console.error("Failed to fetch locations", error);
      setToastConfig(ToastSeverity.Error, "Failed to fetch locations", true);
    } finally {
      setLoading(false);
    }
  };

  // Function to aggregate metrics from all locations
  const aggregateMetricData = (
    allLocationData: any[],
    metric: string,
    daysDifference: number,
    isSameMonthYear: boolean
  ) => {
    const aggregatedDaily: Record<string, number> = {};
    const aggregatedMonthly: Record<string, number> = {};

    // Process each location's data
    allLocationData.forEach((locationData) => {
      if (!locationData || !locationData.multiDailyMetricTimeSeries) return;

      // Handle the nested structure properly
      locationData.multiDailyMetricTimeSeries.forEach((multiMetric: any) => {
        if (!multiMetric || !multiMetric.dailyMetricTimeSeries) return;

        const metricSeries = multiMetric.dailyMetricTimeSeries.find(
          (series: any) => series && series.dailyMetric === metric
        );

        if (
          !metricSeries ||
          !metricSeries.timeSeries ||
          !metricSeries.timeSeries.datedValues
        )
          return;

        // Aggregate values for this location
        for (const entry of metricSeries.timeSeries.datedValues) {
          if (!entry.date) continue;

          const { year, month, day } = entry.date;
          const key =
            daysDifference < VALIDATION_DAYS || isSameMonthYear
              ? dayjs(`${year}-${month}-${day}`).format("YYYY-MMM-DD")
              : dayjs(`${year}-${month}-01`).format("MMM YYYY");

          const value = parseInt(entry.value ?? "0", 10);
          if (daysDifference < VALIDATION_DAYS || isSameMonthYear) {
            aggregatedDaily[key] = (aggregatedDaily[key] || 0) + value;
          } else {
            aggregatedMonthly[key] = (aggregatedMonthly[key] || 0) + value;
          }
        }
      });
    });

    const labels =
      daysDifference < VALIDATION_DAYS || isSameMonthYear
        ? Object.keys(aggregatedDaily).sort()
        : Object.keys(aggregatedMonthly);
    const data =
      daysDifference < VALIDATION_DAYS || isSameMonthYear
        ? labels.map((label) => aggregatedDaily[label] || 0)
        : labels.map((label) => aggregatedMonthly[label] || 0);

    return { data, labels };
  };

  // Function to fetch and aggregate analytics data from selected locations
  const fetchAggregatedAnalyticsData = async (
    from: string,
    to: string,
    isSameMonthYear: boolean,
    selectedLocationIds: string[]
  ) => {
    const daysDifference: number = _applicationHelperService.getDaysDifference(
      from,
      to
    );

    try {
      setLoading(true);
      const requestObj: ILocationMetricsRequestModel = {
        startDate: from,
        endDate: to,
      };

      // Filter locations based on selected location IDs
      const selectedLocations = locationList.filter((location) =>
        selectedLocationIds.includes(location.gmbLocationId)
      );

      if (selectedLocations.length === 0) {
        setToastConfig(
          ToastSeverity.Warning,
          "No valid locations selected",
          true
        );
        return;
      }

      // Fetch data for selected locations
      const allLocationPromises = selectedLocations.map(async (location) => {
        try {
          const getMetricsRequestHeader = {
            "x-gmb-account-id": location.gmbAccountId,
            "x-gmb-location-id": location.gmbLocationId,
          };

          const response = await _locationMetricsService.getLocationMetrics(
            requestObj,
            getMetricsRequestHeader
          );
          return response.data;
        } catch (error) {
          console.error(
            `Failed to fetch data for location ${location.gmbLocationName}:`,
            error
          );
          return null;
        }
      });

      const allLocationData = await Promise.all(allLocationPromises);
      const validLocationData = allLocationData.filter((data) => data !== null);

      console.log("Aggregated Analytics Data:", validLocationData);

      // Create aggregated analytics data structure that matches the expected format
      const aggregatedMultiDailyMetricTimeSeries: Array<{
        dailyMetricTimeSeries: any[];
      }> =
        validLocationData.length > 0
          ? [
              {
                dailyMetricTimeSeries: [],
              },
            ]
          : [];

      // Flatten all dailyMetricTimeSeries from all locations
      const allDailyMetricTimeSeries: any[] = [];
      validLocationData.forEach((locationData) => {
        if (locationData && locationData.multiDailyMetricTimeSeries) {
          locationData.multiDailyMetricTimeSeries.forEach(
            (multiMetric: any) => {
              if (multiMetric && multiMetric.dailyMetricTimeSeries) {
                allDailyMetricTimeSeries.push(
                  ...multiMetric.dailyMetricTimeSeries
                );
              }
            }
          );
        }
      });

      // Group by metric type and aggregate the datedValues
      const groupedMetrics: Record<string, any> = {};
      allDailyMetricTimeSeries.forEach((metric) => {
        if (!metric || !metric.dailyMetric) return;

        if (!groupedMetrics[metric.dailyMetric]) {
          groupedMetrics[metric.dailyMetric] = {
            dailyMetric: metric.dailyMetric,
            timeSeries: {
              datedValues: [],
            },
          };
        }

        if (metric.timeSeries && metric.timeSeries.datedValues) {
          // Instead of pushing all values, we need to aggregate them by date
          metric.timeSeries.datedValues.forEach((datedValue: any) => {
            if (!datedValue || !datedValue.date) return;
            const existingValue = groupedMetrics[
              metric.dailyMetric
            ].timeSeries.datedValues.find(
              (existing: any) =>
                existing.date.year === datedValue.date.year &&
                existing.date.month === datedValue.date.month &&
                existing.date.day === datedValue.date.day
            );

            if (existingValue) {
              // Aggregate the values for the same date
              const currentValue = parseInt(existingValue.value || "0", 10);
              const newValue = parseInt(datedValue.value || "0", 10);
              existingValue.value = (currentValue + newValue).toString();
            } else {
              // Add new date entry
              groupedMetrics[metric.dailyMetric].timeSeries.datedValues.push({
                date: { ...datedValue.date },
                value: datedValue.value || "0",
              });
            }
          });
        }
      });

      // Convert back to array format and ensure we have data
      if (aggregatedMultiDailyMetricTimeSeries.length > 0) {
        aggregatedMultiDailyMetricTimeSeries[0].dailyMetricTimeSeries =
          Object.values(groupedMetrics);
      }

      const aggregatedData: IAggregatedAnalyticsData = {
        multiDailyMetricTimeSeries: aggregatedMultiDailyMetricTimeSeries,
        totalLocations: selectedLocations.length,
        processedLocations: validLocationData.length,
      };

      setAggregatedAnalyticsData(aggregatedData);

      // Debug logging
      console.log("Final aggregated data structure:", aggregatedData);
      if (aggregatedData.multiDailyMetricTimeSeries.length > 0) {
        console.log(
          "First element dailyMetricTimeSeries:",
          aggregatedData.multiDailyMetricTimeSeries[0].dailyMetricTimeSeries
        );
      }

      // Aggregate different metrics
      const webSiteClicksData = aggregateMetricData(
        validLocationData,
        "WEBSITE_CLICKS",
        daysDifference,
        isSameMonthYear
      );
      setWebsiteData(webSiteClicksData);
      handleDataFromChild(webSiteClicksData, "Website clicks");

      const callData = aggregateMetricData(
        validLocationData,
        "CALL_CLICKS",
        daysDifference,
        isSameMonthYear
      );
      setCallData(callData);
      handleDataFromChild(callData, "Calls");

      const directionsData = aggregateMetricData(
        validLocationData,
        "BUSINESS_DIRECTION_REQUESTS",
        daysDifference,
        isSameMonthYear
      );
      setDirectionsData(directionsData);
      handleDataFromChild(directionsData, "Directions");

      setMessagingClicks(
        aggregateMetricData(
          validLocationData,
          "BUSINESS_CONVERSATIONS",
          daysDifference,
          isSameMonthYear
        )
      );

      setBookings(
        aggregateMetricData(
          validLocationData,
          "BUSINESS_FOOD_ORDERS",
          daysDifference,
          isSameMonthYear
        )
      );

      if (validLocationData.length < selectedLocations.length) {
        setToastConfig(
          ToastSeverity.Warning,
          `Data fetched for ${validLocationData.length} out of ${selectedLocations.length} selected locations`,
          true
        );
      }
    } catch (error: any) {
      console.error("Failed to fetch aggregated analytics data", error);
      setWebsiteData(INITIAL_GRAPH_DATA);
      setCallData(INITIAL_GRAPH_DATA);
      setDirectionsData(INITIAL_GRAPH_DATA);
      setMessagingClicks(INITIAL_GRAPH_DATA);
      setBookings(INITIAL_GRAPH_DATA);
      setCount1({ ...count1, total: 0 });
      setCount2({ ...count2, total: 0 });
      setCount3({ ...count3, total: 0 });
      setAggregatedAnalyticsData(null);

      if (error.response?.data?.error) {
        setToastConfig(ToastSeverity.Error, error.response.data.error, true);
      } else if (error.response?.data?.message) {
        setToastConfig(ToastSeverity.Error, error.response.data.message, true);
      } else {
        setToastConfig(
          ToastSeverity.Error,
          "Failed to fetch analytics data",
          true
        );
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDataFromChild = (data: any, type: string) => {
    console.log("Aggregated data from child:", data, type);
    const total =
      data &&
      data.data &&
      data.data.reduce((sum: number, val: number) => sum + val, 0);
    if (type === "Calls") {
      setCount1({
        ...count1,
        total: total,
      });
    } else if (type === "Directions") {
      setCount2({ ...count2, total: total });
    } else if (type === "Website clicks") {
      setCount3({ ...count3, total: total });
    }
  };

  return (
    <div>
      <Box>
        <Box>
          <LeftMenuComponent>
            <Box
              sx={{
                pr: 1,
              }}
            >
              <Box>
                <Box>
                  <Box sx={{ marginBottom: "5px" }}>
                    <h3 className="pageTitle">
                      Analytics - Selected Locations
                    </h3>
                    <Typography variant="subtitle2" className="subtitle2">
                      Hi, {userInfo && userInfo.name}. Select business, account,
                      and locations to view analytics data.
                    </Typography>
                    {aggregatedAnalyticsData && (
                      <Typography
                        variant="body2"
                        sx={{ mt: 1, color: "text.secondary" }}
                      >
                        Showing data from{" "}
                        {aggregatedAnalyticsData.processedLocations} out of{" "}
                        {aggregatedAnalyticsData.totalLocations} selected
                        locations
                      </Typography>
                    )}
                  </Box>

                  <Formik
                    initialValues={INITIAL_VALUES}
                    validationSchema={AnalyticsSchema}
                    onSubmit={handleFormSubmit}
                  >
                    {({
                      values,
                      errors,
                      touched,
                      handleChange,
                      handleBlur,
                      handleSubmit,
                      setFieldValue,
                      isValid,
                    }) => (
                      <form onSubmit={handleSubmit}>
                        <Grid container spacing={2}>
                          <Grid item xs={12} md={6} lg={2}>
                            <FormControl
                              className="commonSelect"
                              variant="filled"
                              fullWidth
                              error={Boolean(
                                getIn(errors, "businessId") &&
                                  getIn(touched, "businessId")
                              )}
                            >
                              <InputLabel id="business-dropdown-label">
                                Business
                              </InputLabel>
                              <Select
                                fullWidth
                                id="businessId"
                                label="Business"
                                value={values.businessId.toString()}
                                onChange={(evt: SelectChangeEvent) => {
                                  setFieldValue("businessGroupId", "0");
                                  setFieldValue("locationIds", []);
                                  setFieldValue(
                                    "businessId",
                                    +evt.target.value
                                  );
                                }}
                                onBlur={handleBlur}
                                sx={{
                                  backgroundColor: "var(--whiteColor)",
                                  borderRadius: "5px",
                                }}
                              >
                                <MenuItem value={0}>Select</MenuItem>
                                {businessList &&
                                  businessList.map((business: IBusiness) => (
                                    <MenuItem
                                      key={business.id}
                                      value={business.id.toString()}
                                    >
                                      {business.businessName}
                                    </MenuItem>
                                  ))}
                              </Select>
                              <FormHelperText>
                                {touched.businessId && errors.businessId
                                  ? errors.businessId
                                  : ""}
                              </FormHelperText>
                            </FormControl>
                          </Grid>

                          <Grid item xs={12} md={6} lg={2}>
                            <FormControl
                              className="commonSelect"
                              variant="filled"
                              fullWidth
                              error={Boolean(
                                getIn(errors, "businessGroupId") &&
                                  getIn(touched, "businessGroupId")
                              )}
                            >
                              <InputLabel id="account-dropdown-label">
                                Account
                              </InputLabel>
                              <Select
                                fullWidth
                                id="businessGroupId"
                                label="Account"
                                value={values.businessGroupId.toString()}
                                onChange={(evt: SelectChangeEvent) => {
                                  setFieldValue(
                                    "businessGroupId",
                                    evt.target.value
                                  );
                                  setFieldValue("locationIds", []);
                                }}
                                onBlur={handleBlur}
                                sx={{
                                  backgroundColor: "var(--whiteColor)",
                                  borderRadius: "5px",
                                }}
                              >
                                <MenuItem value={"0"}>Select</MenuItem>
                                {businessGroups
                                  .filter(
                                    (x: IBusinessGroup) =>
                                      x.businessId === values.businessId
                                  )
                                  .map((businessGroup: IBusinessGroup) => (
                                    <MenuItem
                                      key={businessGroup.accountId}
                                      value={businessGroup.accountId.toString()}
                                    >
                                      {businessGroup.accountName}
                                    </MenuItem>
                                  ))}
                              </Select>
                              <FormHelperText>
                                {touched.businessGroupId &&
                                errors.businessGroupId
                                  ? errors.businessGroupId
                                  : ""}
                              </FormHelperText>
                            </FormControl>
                          </Grid>

                          <Grid item xs={12} md={6} lg={3}>
                            <FormControl
                              className="commonSelect"
                              variant="filled"
                              fullWidth
                              error={Boolean(
                                getIn(errors, "locationIds") &&
                                  getIn(touched, "locationIds")
                              )}
                            >
                              <InputLabel id="locations-dropdown-label">
                                Locations
                              </InputLabel>
                              <Select
                                fullWidth
                                multiple
                                id="locationIds"
                                label="Locations"
                                value={values.locationIds}
                                onChange={(
                                  event: SelectChangeEvent<string[]>
                                ) => {
                                  const {
                                    target: { value },
                                  } = event;

                                  const availableLocationIds = locationList
                                    .filter(
                                      (x: ILocation) =>
                                        x.gmbAccountId ===
                                        values.businessGroupId
                                    )
                                    .map((location) => location.gmbLocationId);

                                  const newValue =
                                    typeof value === "string" ? [value] : value;
                                  const currentValue = values.locationIds;

                                  // Handle Select All
                                  if (newValue.includes("select-all")) {
                                    if (currentValue.includes("select-all")) {
                                      // Deselect all
                                      setFieldValue("locationIds", []);
                                    } else {
                                      // Select all
                                      setFieldValue("locationIds", [
                                        "select-all",
                                        ...availableLocationIds,
                                      ]);
                                    }
                                  } else {
                                    // Handle individual selections
                                    const selectedLocations = newValue.filter(
                                      (id: string) => id !== "select-all"
                                    );

                                    // Check if all locations are selected
                                    if (
                                      selectedLocations.length ===
                                      availableLocationIds.length
                                    ) {
                                      // All selected, add select-all
                                      setFieldValue("locationIds", [
                                        "select-all",
                                        ...selectedLocations,
                                      ]);
                                    } else {
                                      // Not all selected, just set the locations
                                      setFieldValue(
                                        "locationIds",
                                        selectedLocations
                                      );
                                    }
                                  }
                                }}
                                input={<OutlinedInput label="Locations" />}
                                MenuProps={MenuProps}
                                renderValue={(selected) => {
                                  const selectedArray = selected as string[];
                                  const isSelectAllChecked =
                                    selectedArray.includes("select-all");
                                  const availableLocationIds = locationList
                                    .filter(
                                      (x: ILocation) =>
                                        x.gmbAccountId ===
                                        values.businessGroupId
                                    )
                                    .map((location) => location.gmbLocationId);

                                  if (
                                    isSelectAllChecked &&
                                    selectedArray.length - 1 ===
                                      availableLocationIds.length
                                  ) {
                                    // All locations are selected
                                    return (
                                      <Box
                                        sx={{
                                          display: "flex",
                                          alignItems: "center",
                                        }}
                                      >
                                        <Chip
                                          label={`All Locations (${availableLocationIds.length})`}
                                          size="small"
                                          color="primary"
                                        />
                                      </Box>
                                    );
                                  }

                                  return (
                                    <Box
                                      sx={{
                                        display: "flex",
                                        flexWrap: "wrap",
                                        gap: 0.5,
                                      }}
                                    >
                                      {selectedArray
                                        .filter((id) => id !== "select-all")
                                        .map((value) => {
                                          const location = locationList.find(
                                            (loc) => loc.gmbLocationId === value
                                          );
                                          return (
                                            <Chip
                                              key={value}
                                              label={
                                                location?.gmbLocationName ||
                                                value
                                              }
                                              size="small"
                                            />
                                          );
                                        })}
                                    </Box>
                                  );
                                }}
                                sx={{
                                  backgroundColor: "var(--whiteColor)",
                                  borderRadius: "5px",
                                }}
                              >
                                {/* Only show options if account is selected */}
                                {values.businessGroupId !== "0" ? (
                                  <>
                                    {/* Select All option */}
                                    <MenuItem
                                      value="select-all"
                                      sx={{
                                        borderBottom:
                                          "1px solid rgba(0, 0, 0, 0.12)",
                                        fontWeight: "bold",
                                        paddingY: 1,
                                        whiteSpace: "nowrap",
                                        overflow: "visible",
                                      }}
                                    >
                                      <Checkbox
                                        checked={values.locationIds.includes(
                                          "select-all"
                                        )}
                                        sx={{ mr: 1 }}
                                        onClick={(e) => e.stopPropagation()}
                                      />
                                      <ListItemText
                                        primary="Select All Locations"
                                        sx={{ whiteSpace: "nowrap" }}
                                      />
                                    </MenuItem>

                                    {/* Individual locations */}
                                    {locationList
                                      .filter(
                                        (x: ILocation) =>
                                          x.gmbAccountId ===
                                          values.businessGroupId
                                      )
                                      .map((location: ILocation) => (
                                        <MenuItem
                                          key={location.gmbLocationId}
                                          value={location.gmbLocationId}
                                          sx={{
                                            whiteSpace: "nowrap",
                                            overflow: "visible",
                                          }}
                                        >
                                          <Checkbox
                                            checked={values.locationIds.includes(
                                              location.gmbLocationId
                                            )}
                                            sx={{ mr: 1 }}
                                            onClick={(e) => e.stopPropagation()}
                                          />
                                          <ListItemText
                                            primary={location.gmbLocationName}
                                            sx={{ whiteSpace: "nowrap" }}
                                          />
                                        </MenuItem>
                                      ))}
                                  </>
                                ) : (
                                  <MenuItem disabled>
                                    Please select an account first
                                  </MenuItem>
                                )}
                              </Select>
                              <FormHelperText>
                                {touched.locationIds && errors.locationIds
                                  ? errors.locationIds
                                  : ""}
                              </FormHelperText>
                            </FormControl>
                          </Grid>

                          <Grid item xs={12} md={6} lg={2}>
                            <DateFilter
                              onDateChange={(range: any) => {
                                setSelectedDateRange(range);
                              }}
                            />
                          </Grid>

                          <Grid item xs={12} md={6} lg={3}>
                            <FormControl
                              className="commonSelect"
                              variant="filled"
                              fullWidth
                              sx={{ height: "100%" }}
                            >
                              <Button
                                type="submit"
                                variant="contained"
                                disabled={
                                  !isValid ||
                                  !selectedDateRange ||
                                  values.locationIds.length === 0
                                }
                                sx={{
                                  height: "56px",
                                  width: "100%",
                                  mt: "16px", // Match the margin top of other form fields
                                }}
                              >
                                Load Analytics
                              </Button>
                            </FormControl>
                          </Grid>
                        </Grid>
                      </form>
                    )}
                  </Formik>
                  <Divider style={{ margin: "20px 0", height: 0 }} />

                  {/* Only show analytics data if it's available */}
                  {aggregatedAnalyticsData ? (
                    <>
                      <Box>
                        <Grid
                          container
                          spacing={2}
                          className="commonCardBottomSpacing"
                        >
                          <Grid item xs={12} md={4} lg={4}>
                            <Box className="commonCard dashboardTopIconCard">
                              <Box className="dashboardTopIcon">
                                <img
                                  alt="MyLocoBiz - Logo"
                                  className="innerImage1"
                                  src={require("../../assets/dashboard/Picture2.png")}
                                />
                              </Box>
                              <Box className="dashboardTopInfo">
                                <Typography className="dashboardTopCount">
                                  {count1.total}
                                </Typography>
                                <Typography className="dashboardTopTitle">
                                  {count1.type} (All Locations)
                                </Typography>
                              </Box>
                            </Box>
                          </Grid>
                          <Grid item xs={12} md={4} lg={4}>
                            <Box className="commonCard dashboardTopIconCard">
                              <Box className="dashboardTopIcon">
                                <img
                                  alt="MyLocoBiz - Logo"
                                  className="innerImage2"
                                  src={require("../../assets/dashboard/Picture1.png")}
                                />
                              </Box>
                              <Box className="dashboardTopInfo">
                                <Typography className="dashboardTopCount">
                                  {count2.total}
                                </Typography>
                                <Typography className="dashboardTopTitle">
                                  {count2.type} (All Locations)
                                </Typography>
                              </Box>
                            </Box>
                          </Grid>
                          <Grid item xs={12} md={4} lg={4}>
                            <Box className="commonCard dashboardTopIconCard">
                              <Box className="dashboardTopIcon">
                                <img
                                  alt="MyLocoBiz - Logo"
                                  className="innerImage3"
                                  src={require("../../assets/dashboard/Picture3.png")}
                                />
                              </Box>
                              <Box className="dashboardTopInfo">
                                <Typography className="dashboardTopCount">
                                  {count3.total}
                                </Typography>
                                <Typography className="dashboardTopTitle">
                                  {count3.type} (All Locations)
                                </Typography>
                              </Box>
                            </Box>
                          </Grid>
                        </Grid>

                        <Grid
                          container
                          spacing={2}
                          className="commonCardBottomSpacing"
                        >
                          {/* BusinessProfileInteractionsChart temporarily replaced with summary card */}

                          {/* Show a placeholder message for now */}
                          <Grid item xs={12} md={12}>
                            <Box
                              className="commonCard"
                              sx={{ p: 3, textAlign: "center" }}
                            >
                              <Typography variant="h6" gutterBottom>
                                Business Profile Interactions (Aggregated)
                              </Typography>
                              <Typography
                                variant="body2"
                                color="text.secondary"
                              >
                                Aggregated interactions data from{" "}
                                {locationList.length} location(s)
                              </Typography>
                              <Typography
                                variant="h4"
                                sx={{ mt: 2, color: "primary.main" }}
                              >
                                {(
                                  count1.total +
                                  count2.total +
                                  count3.total
                                ).toLocaleString()}
                              </Typography>
                              <Typography
                                variant="caption"
                                color="text.secondary"
                              >
                                Total interactions (Calls + Directions + Website
                                Clicks)
                              </Typography>
                            </Box>
                          </Grid>

                          <Grid item xs={12} md={6} lg={6}>
                            <Box className="commonCard height100 pieChartDiv">
                              <Box
                                sx={{
                                  p: 2,
                                  backgroundColor: "#fff",
                                  borderRadius: 2,
                                }}
                              >
                                <PlatformBreakdownChart
                                  data={{
                                    multiDailyMetricTimeSeries:
                                      aggregatedAnalyticsData?.multiDailyMetricTimeSeries ||
                                      [],
                                  }}
                                />
                              </Box>
                            </Box>
                          </Grid>
                          <Grid item xs={12} md={6} lg={6}>
                            <RevenueChartDashboard
                              data1={callData.data}
                              data2={directionsData.data}
                              labels={directionsData.labels}
                              title1="Call Data (All Locations)"
                              title2="Directions Data (All Locations)"
                              graphTitle="Directions Vs Calls - Aggregated"
                            />
                          </Grid>
                        </Grid>
                      </Box>
                      <Box>
                        <Grid
                          container
                          spacing={2}
                          className="commonCardBottomSpacing"
                        >
                          <Grid item xs={12} md={6}>
                            <WebsiteClicksChart
                              {...callData}
                              title="Call clicks made from all Business Profiles"
                              graphTitle="Calls (Aggregated)"
                              colorCode={0}
                            />
                          </Grid>

                          <Grid item xs={12} md={6}>
                            <WebsiteClicksChart
                              {...messagingClicks}
                              title="Messaging clicks made from all Business Profiles"
                              graphTitle="Messaging clicks (Aggregated)"
                              colorCode={1}
                            />
                          </Grid>

                          <Grid item xs={12} md={6}>
                            <WebsiteClicksChart
                              {...bookings}
                              title="Bookings made from all Business Profiles"
                              graphTitle="Bookings (Aggregated)"
                              colorCode={0}
                            />
                          </Grid>

                          <Grid item xs={12} md={6}>
                            <WebsiteClicksChart
                              {...directionsData}
                              title="Direction requests made from all Business Profiles"
                              graphTitle="Directions (Aggregated)"
                              colorCode={1}
                            />
                          </Grid>

                          <Grid item xs={12} md={6}>
                            <WebsiteClicksChart
                              {...websiteData}
                              title="Website clicks made from all Business Profiles"
                              graphTitle="Website clicks (Aggregated)"
                              colorCode={0}
                            />
                          </Grid>

                          <Grid item xs={12} md={6}>
                            <Box className="commonCard height100 pieChartDiv">
                              <HomeChartCard />
                            </Box>
                          </Grid>
                        </Grid>
                      </Box>
                    </>
                  ) : (
                    <Box sx={{ textAlign: "center", py: 4 }}>
                      <Typography variant="h6" color="text.secondary">
                        Please select business, account, locations, and date
                        range to view analytics data
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Box>
            </Box>
          </LeftMenuComponent>
        </Box>
      </Box>
    </div>
  );
};

export default Analytics;
