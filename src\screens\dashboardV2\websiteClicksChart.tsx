import React, { useEffect, useMemo } from "react";
import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Tooltip,
  Filler,
} from "chart.js";
import { Line } from "react-chartjs-2";
import { Box, Typography } from "@mui/material";
import { theme } from "../../theme";

ChartJS.register(
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Tooltip,
  Filler
);

interface Props {
  data: number[];
  labels: string[];
  title: string; // e.g. "Website clicks made from your Business Profile"
  graphTitle: string;
  colorCode: number;
  onSendData?: (data: string, type: string) => void;
}

const WebsiteClicksChart: React.FC<Props> = ({
  data,
  labels,
  title,
  graphTitle,
  colorCode,
  onSendData,
}) => {
  const total = data && data.reduce((sum, val) => sum + val, 0);
  // const total = useMemo(() => {
  //   return data.reduce((sum, val) => sum + val, 0);
  // }, [data]);

  // useEffect(() => {
  //   if (onSendData) {
  //     onSendData(total.toString(), graphTitle);
  //   }
  // }, [total]);

  const colorCodes = [
    {
      color: theme.palette.secondary?.main,
      backgroundColor: theme.palette.secondaryAlpha?.main,
    },
    {
      color: theme.palette.primary?.main,
      backgroundColor: theme.palette.primaryAlpha?.main,
    },
  ];

  const chartData = {
    labels,
    datasets: [
      {
        label: title,
        data,
        backgroundColor: colorCodes[colorCode].backgroundColor, // updated fill color
        borderColor: colorCodes[colorCode].color, // updated line color
        fill: true,
        tension: 0, // straight lines
        pointBackgroundColor: colorCodes[colorCode].color, // updated point color
        pointRadius: 5,
      },
    ],
  };
  return (
    <Box sx={{ p: 2, backgroundColor: "#fff", borderRadius: 2 }}>
      <Box sx={{ flexGrow: 1 }}>
        <Box sx={{ p: 3, borderRadius: 2 }}>
          <Typography variant="h5" fontWeight="bold" gutterBottom>
            {graphTitle}
          </Typography>
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            {total}
          </Typography>
          <Typography variant="subtitle2" sx={{ mb: 2 }}>
            {title}
          </Typography>
          <Line
            data={chartData}
            options={{
              responsive: true,
              plugins: { legend: { display: false } },
            }}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default WebsiteClicksChart;
