import React, { useEffect, useMemo } from "react";
import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Tooltip,
  Filler,
} from "chart.js";
import { Line } from "react-chartjs-2";
import { Box, Typography } from "@mui/material";
import { theme } from "../../theme";
import ExportButton from "../../components/exportButton/exportButton.component";
import { ChartExportData } from "../../services/excelExport.service";
import { ILocation } from "../../interfaces/response/ILocationsListResponseModel";

ChartJS.register(
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Tooltip,
  Filler
);

interface Props {
  data: number[];
  labels: string[];
  title: string; // e.g. "Website clicks made from your Business Profile"
  graphTitle: string;
  colorCode: number;
  onSendData?: (data: string, type: string) => void;
  // Export functionality props (only visible in analytics)
  showExport?: boolean;
  selectedLocationIds?: string[];
  availableLocations?: ILocation[];
  dateRange?: {
    from: string;
    to: string;
  };
}

const WebsiteClicksChart: React.FC<Props> = ({
  data,
  labels,
  title,
  graphTitle,
  colorCode,
  onSendData,
  showExport = false,
  selectedLocationIds = [],
  availableLocations = [],
  dateRange,
}) => {
  const total = data && data.reduce((sum, val) => sum + val, 0);

  // Prepare export data
  const exportData: ChartExportData = {
    chartTitle: graphTitle,
    data: { data, labels },
    selectedLocationIds,
    availableLocations,
    dateRange,
  };

  const colorCodes = [
    {
      color: theme.palette.secondary?.main,
      backgroundColor: theme.palette.secondaryAlpha?.main,
    },
    {
      color: theme.palette.primary?.main,
      backgroundColor: theme.palette.primaryAlpha?.main,
    },
  ];

  const chartData = {
    labels,
    datasets: [
      {
        label: title,
        data,
        backgroundColor: colorCodes[colorCode].backgroundColor, // updated fill color
        borderColor: colorCodes[colorCode].color, // updated line color
        fill: true,
        tension: 0, // straight lines
        pointBackgroundColor: colorCodes[colorCode].color, // updated point color
        pointRadius: 5,
      },
    ],
  };
  return (
    <Box sx={{ p: 2, backgroundColor: "#fff", borderRadius: 2 }}>
      <Box sx={{ flexGrow: 1 }}>
        <Box sx={{ p: 3, borderRadius: 2 }}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "flex-start",
              mb: 1,
            }}
          >
            <Typography variant="h5" fontWeight="bold" gutterBottom>
              {graphTitle}
            </Typography>
            {showExport && (
              <ExportButton
                chartData={exportData}
                size="small"
                tooltipText={`Export ${graphTitle} data to Excel`}
              />
            )}
          </Box>
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            {total}
          </Typography>
          <Typography variant="subtitle2" sx={{ mb: 2 }}>
            {title}
          </Typography>
          <Line
            data={chartData}
            options={{
              responsive: true,
              plugins: { legend: { display: false } },
            }}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default WebsiteClicksChart;
