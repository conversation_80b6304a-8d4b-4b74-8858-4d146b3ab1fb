import React from "react";
import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Tooltip,
  Legend,
  ChartOptions,
  ChartData,
} from "chart.js";
import { Line } from "react-chartjs-2";
import { Box, Typography } from "@mui/material";
import type { GridLineOptions } from "chart.js";
import { theme } from "../../theme";
import ExportButton from "../exportButton/exportButton.component";
import { ChartExportData } from "../../services/excelExport.service";
import { ILocation } from "../../interfaces/response/ILocationsListResponseModel";

ChartJS.register(
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Tooltip,
  Legend
);

interface Props {
  data1: number[];
  data2: number[];
  labels: string[];
  title1: string; // e.g. "Website clicks made from your Business Profile"
  title2: string;
  graphTitle: string;
  // Export functionality props (only visible in analytics)
  showExport?: boolean;
  selectedLocationIds?: string[];
  availableLocations?: ILocation[];
  dateRange?: {
    from: string;
    to: string;
  };
}

const options: ChartOptions<"line"> = {
  responsive: true,
  plugins: {
    // ...
  },
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        callback: (value) => `${Number(value)}`,
      },
      grid: {
        drawBorder: false, // ✅ Should now be accepted
      } as Partial<GridLineOptions>, // <- TypeScript fix
    },
    x: {
      grid: {
        display: false,
      },
    },
  },
};

const RevenueChartDashboard: React.FC<Props> = ({
  data1,
  data2,
  labels,
  title1,
  title2,
  graphTitle,
  showExport = false,
  selectedLocationIds = [],
  availableLocations = [],
  dateRange,
}) => {
  // Prepare export data for the combined chart
  const exportData: ChartExportData = {
    chartTitle: graphTitle,
    data: {
      data: [...data1, ...data2], // Combine both datasets
      labels: [...labels, ...labels.map((label) => `${label} (${title2})`)], // Add labels for both datasets
    },
    selectedLocationIds,
    availableLocations,
    dateRange,
  };
  const data: ChartData<"line"> = {
    labels,
    datasets: [
      {
        label: title1,
        data: data1,
        borderColor: theme.palette.primary?.main,
        backgroundColor: theme.palette.primary?.main,
        fill: false,
        tension: 0,
        pointRadius: 5,
        pointHoverRadius: 7,
        pointBackgroundColor: theme.palette.primary?.main,
      },
      {
        label: title2,
        data: data2,
        borderColor: theme.palette.secondary?.main,
        backgroundColor: theme.palette.secondary?.main,
        fill: false,
        tension: 0,
        pointRadius: 5,
        pointHoverRadius: 7,
        pointBackgroundColor: theme.palette.secondary?.main,
      },
    ],
  };

  return (
    <Box sx={{ p: 2, backgroundColor: "#fff", borderRadius: 2 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
        }}
      >
        <Typography variant="h6" fontWeight="bold">
          {graphTitle}
        </Typography>
        {showExport && (
          <ExportButton
            chartData={exportData}
            size="small"
            tooltipText={`Export ${graphTitle} data to Excel`}
          />
        )}
      </Box>
      <div style={{ height: "450px" }}>
        <Line data={data} options={options} />
      </div>
    </Box>
  );
};

export default RevenueChartDashboard;
