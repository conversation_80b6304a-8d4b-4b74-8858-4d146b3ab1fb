import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { ILocation } from "../interfaces/response/ILocationsListResponseModel";

// Define the interface locally since it's used in multiple places
interface IGraphDataModel {
  data: number[];
  labels: string[];
}

export interface ChartExportData {
  chartTitle: string;
  data: IGraphDataModel;
  selectedLocationIds: string[];
  availableLocations: ILocation[];
  locationDataMap: Record<string, any>; // Individual location data
  dateRange?: {
    from: string;
    to: string;
  };
}

export class ExcelExportService {
  /**
   * Export chart data to Excel file
   */
  static exportChartToExcel(exportData: ChartExportData): void {
    const {
      chartTitle,
      data,
      selectedLocationIds,
      availableLocations,
      dateRange,
    } = exportData;

    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Prepare chart data with new matrix format
    const chartData = this.prepareChartData(exportData);

    // Prepare location information
    const locationData = this.prepareLocationData(
      selectedLocationIds,
      availableLocations
    );

    // Prepare summary information
    const summaryData = this.prepareSummaryData(
      chartTitle,
      data,
      dateRange,
      selectedLocationIds.length
    );

    // Create worksheets
    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    const chartSheet = XLSX.utils.aoa_to_sheet(chartData);
    const locationSheet = XLSX.utils.aoa_to_sheet(locationData);

    // Add worksheets to workbook
    XLSX.utils.book_append_sheet(workbook, summarySheet, "Summary");
    XLSX.utils.book_append_sheet(workbook, chartSheet, "Chart Data");
    XLSX.utils.book_append_sheet(workbook, locationSheet, "Locations");

    // Set column widths for better readability - adjust for new format
    this.setColumnWidths(summarySheet, [20, 30]);
    // Dynamic column widths based on number of locations
    const chartColumnWidths = [15, ...selectedLocationIds.map(() => 20), 15]; // Date, locations, total
    this.setColumnWidths(chartSheet, chartColumnWidths);
    this.setColumnWidths(locationSheet, [15, 40, 30]);

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");
    const filename = `${chartTitle.replace(
      /[^a-zA-Z0-9]/g,
      "_"
    )}_${timestamp}.xlsx`;

    // Export the file
    const excelBuffer = XLSX.write(workbook, {
      bookType: "xlsx",
      type: "array",
    });
    const blob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    saveAs(blob, filename);
  }

  /**
   * Prepare chart data for Excel export with locations as columns and dates as rows
   */
  private static prepareChartData(exportData: ChartExportData): any[][] {
    const {
      chartTitle,
      selectedLocationIds,
      availableLocations,
      locationDataMap,
      data,
    } = exportData;
    const chartData: any[][] = [];

    // Add header
    chartData.push([chartTitle]);
    chartData.push([]); // Empty row

    // Get location names for selected locations
    const selectedLocations = selectedLocationIds.map((locationId) => {
      const location = availableLocations.find(
        (loc) => loc.gmbLocationId === locationId
      );
      return location ? location.gmbLocationName : locationId;
    });

    // Create header row: Date | Location1 | Location2 | ... | Total
    const headerRow = ["Date", ...selectedLocations, "Total"];
    chartData.push(headerRow);

    // Get all unique dates from the aggregated data
    const dates = data.labels || [];

    // Create data rows
    dates.forEach((date, dateIndex) => {
      const row: any[] = [date]; // Start with date
      let rowTotal = 0;

      // Add data for each location
      selectedLocationIds.forEach((locationId) => {
        const locationData = locationDataMap[locationId];
        let value = 0;

        if (
          locationData &&
          locationData.data &&
          locationData.data[dateIndex] !== undefined
        ) {
          value = locationData.data[dateIndex] || 0;
        }

        row.push(value);
        rowTotal += value;
      });

      // Add row total
      row.push(rowTotal);
      chartData.push(row);
    });

    // Add totals row
    chartData.push([]); // Empty row
    const totalsRow: any[] = ["Total"];
    let grandTotal = 0;

    selectedLocationIds.forEach((locationId) => {
      const locationData = locationDataMap[locationId];
      const locationTotal =
        locationData && locationData.data
          ? locationData.data.reduce(
              (sum: number, value: number) => sum + (value || 0),
              0
            )
          : 0;
      totalsRow.push(locationTotal);
      grandTotal += locationTotal;
    });

    totalsRow.push(grandTotal);
    chartData.push(totalsRow);

    return chartData;
  }

  /**
   * Prepare location data for Excel export
   */
  private static prepareLocationData(
    selectedLocationIds: string[],
    availableLocations: ILocation[]
  ): any[][] {
    const locationData: any[][] = [];

    // Add header
    locationData.push(["Selected Locations"]);
    locationData.push([]); // Empty row
    locationData.push(["Location ID", "Location Name", "Account ID"]);

    // Add location rows
    selectedLocationIds.forEach((locationId) => {
      const location = availableLocations.find(
        (loc) => loc.gmbLocationId === locationId
      );
      if (location) {
        locationData.push([
          location.gmbLocationId,
          location.gmbLocationName,
          location.gmbAccountId,
        ]);
      }
    });

    return locationData;
  }

  /**
   * Prepare summary data for Excel export
   */
  private static prepareSummaryData(
    chartTitle: string,
    data: IGraphDataModel,
    dateRange?: { from: string; to: string },
    locationCount?: number
  ): any[][] {
    const summaryData: any[][] = [];

    // Add title and metadata
    summaryData.push(["Analytics Export Summary"]);
    summaryData.push([]); // Empty row
    summaryData.push(["Chart Title", chartTitle]);
    summaryData.push(["Export Date", new Date().toLocaleString()]);

    if (dateRange) {
      summaryData.push(["Date Range", `${dateRange.from} to ${dateRange.to}`]);
    }

    if (locationCount) {
      summaryData.push(["Number of Locations", locationCount]);
    }

    // Add data summary
    const total = data.data
      ? data.data.reduce((sum: number, value: number) => sum + (value || 0), 0)
      : 0;
    const average =
      data.data && data.data.length > 0 ? total / data.data.length : 0;
    const max = data.data && data.data.length > 0 ? Math.max(...data.data) : 0;
    const min = data.data && data.data.length > 0 ? Math.min(...data.data) : 0;

    summaryData.push([]); // Empty row
    summaryData.push(["Data Summary", ""]);
    summaryData.push(["Total Value", total]);
    summaryData.push(["Average Value", Math.round(average * 100) / 100]);
    summaryData.push(["Maximum Value", max]);
    summaryData.push(["Minimum Value", min]);
    summaryData.push(["Data Points", data.data ? data.data.length : 0]);

    return summaryData;
  }

  /**
   * Set column widths for better readability
   */
  private static setColumnWidths(
    sheet: XLSX.WorkSheet,
    widths: number[]
  ): void {
    if (!sheet["!cols"]) {
      sheet["!cols"] = [];
    }

    widths.forEach((width, index) => {
      if (!sheet["!cols"]![index]) {
        sheet["!cols"]![index] = {};
      }
      sheet["!cols"]![index].wch = width;
    });
  }

  /**
   * Export multiple charts to a single Excel file
   */
  static exportMultipleChartsToExcel(
    charts: ChartExportData[],
    filename: string = "Analytics_Export"
  ): void {
    const workbook = XLSX.utils.book_new();

    // Add summary sheet with all charts overview
    const overviewData = this.prepareOverviewData(charts);
    const overviewSheet = XLSX.utils.aoa_to_sheet(overviewData);
    XLSX.utils.book_append_sheet(workbook, overviewSheet, "Overview");

    // Add individual chart sheets
    charts.forEach((chartData, index) => {
      const chartSheetData = this.prepareChartData(chartData);
      const chartSheet = XLSX.utils.aoa_to_sheet(chartSheetData);
      const sheetName = `Chart_${index + 1}_${chartData.chartTitle
        .substring(0, 20)
        .replace(/[^a-zA-Z0-9]/g, "_")}`;
      XLSX.utils.book_append_sheet(workbook, chartSheet, sheetName);
    });

    // Add locations sheet
    if (charts.length > 0) {
      const locationData = this.prepareLocationData(
        charts[0].selectedLocationIds,
        charts[0].availableLocations
      );
      const locationSheet = XLSX.utils.aoa_to_sheet(locationData);
      XLSX.utils.book_append_sheet(workbook, locationSheet, "Locations");
    }

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");
    const finalFilename = `${filename}_${timestamp}.xlsx`;

    // Export the file
    const excelBuffer = XLSX.write(workbook, {
      bookType: "xlsx",
      type: "array",
    });
    const blob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    saveAs(blob, finalFilename);
  }

  /**
   * Prepare overview data for multiple charts export
   */
  private static prepareOverviewData(charts: ChartExportData[]): any[][] {
    const overviewData: any[][] = [];

    overviewData.push(["Analytics Export Overview"]);
    overviewData.push([]); // Empty row
    overviewData.push(["Export Date", new Date().toLocaleString()]);
    overviewData.push(["Number of Charts", charts.length]);

    if (charts.length > 0 && charts[0].dateRange) {
      overviewData.push([
        "Date Range",
        `${charts[0].dateRange.from} to ${charts[0].dateRange.to}`,
      ]);
    }

    overviewData.push([
      "Number of Locations",
      charts[0]?.selectedLocationIds.length || 0,
    ]);
    overviewData.push([]); // Empty row

    // Add chart summaries
    overviewData.push(["Chart Summaries", "", ""]);
    overviewData.push(["Chart Title", "Total Value", "Data Points"]);

    charts.forEach((chart) => {
      const total = chart.data.data
        ? chart.data.data.reduce(
            (sum: number, value: number) => sum + (value || 0),
            0
          )
        : 0;
      const dataPoints = chart.data.data ? chart.data.data.length : 0;
      overviewData.push([chart.chartTitle, total, dataPoints]);
    });

    return overviewData;
  }
}
