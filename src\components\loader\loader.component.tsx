import React from "react";
import LocoBizIcon from "../../assets/common/LocoBizIcon.png";
import "./loader.component.css";

interface LoaderProps {
  animationType?: "default" | "slow" | "pulse" | "bounce";
  theme?: "light" | "dark" | "gradient";
  showText?: boolean;
  text?: string;
}

const Loader: React.FC<LoaderProps> = ({
  animationType = "default",
  theme = "light",
  showText = true,
  text = "Loading...",
}) => {
  const getLogoClassName = () => {
    switch (animationType) {
      case "slow":
        return "rotating-logo-slow";
      case "pulse":
        return "rotating-logo-pulse";
      case "bounce":
        return "rotating-logo-bounce";
      default:
        return "rotating-logo";
    }
  };

  const getContainerClassName = () => {
    let className = "rotating-loader-container";
    if (theme === "dark") {
      className += " dark-theme";
    } else if (theme === "gradient") {
      className += " gradient-theme";
    }
    return className;
  };

  return (
    <div className={getContainerClassName()}>
      <img
        src={LocoBizIcon}
        alt="LocoBiz Loading..."
        className={getLogoClassName()}
      />
      {showText && <div className="loading-text">{text}</div>}
    </div>
  );
};

export default Loader;
