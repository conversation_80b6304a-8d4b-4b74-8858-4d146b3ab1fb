import React, {
  use<PERSON>allback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import "@fontsource/barlow";
import logo from "./logo.svg";
import "./App.css";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import { ThreeCircles } from "react-loader-spinner";
import { LoadingContext } from "./context/loading.context";
import Box from "@mui/material/Box";
import { PreferencesContext } from "./context/preferences.context";
import { Routes, Route, useNavigate, useLocation } from "react-router-dom";
import SignIn from "./screens/signIn/signIn.screen";
import { theme } from "./theme";
import Dashboard from "./screens/dashboard/dashboard.screen";
import Roles from "./screens/userManagement/roles/roles.screen";
import ForgotPassword from "./screens/forgotPassword/forgotPassword.screen";
import Users from "./screens/userManagement/users/users.screen";
import Analytics from "./screens/analytics/analytics.screen";
import QandA from "./screens/qanda/qanda.screen";
import ManageBusiness from "./screens/businessManagement/manageBusiness/manageBusiness.screen";
import ManageReviews from "./screens/reviewManagement/manageReviews/manageReviews.screen";
import Help from "./screens/help/help.screen";
import { useDispatch, useSelector } from "react-redux";
import Snackbar from "@mui/material/Snackbar";
import MuiAlert, { AlertProps } from "@mui/material/Alert";
import { ToastSeverity } from "./constants/toastSeverity.constant";
import { ToastContext } from "./context/toast.context";
import LocalBusiness from "./screens/businessManagement/localBusiness/localBusiness.screen";
import BusinessSummary from "./screens/businessManagement/businessSummary/businessSummary.screen";
import CreateSocialPost from "./screens/createSocialPost/createSocialPost.screen";
import GMBCallback from "./screens/businessManagement/callback/callback.screen";
import PostsListing from "./screens/posts/listing/PostListing.screen";
import { sessionExpired } from "./actions/auth.actions";
import NotFoundPage from "./components/notFoundPage/notFoundPage.component";
import DashboardV2 from "./screens/dashboardV2/dashboardV2.screen";
import UnAuthorized from "./components/unAuthorized/notFoundPage.component";
import BusinessCategoryScreen from "./screens/businessCategory/businessCategory.screen";
import DemoScreen from "./screens/businessCategory/demo.screen";
import ServicesDemoScreen from "./screens/businessCategory/servicesDemo.screen";
import Loader from "./components/loader/loader.component";
import LoaderDemo from "./screens/loaderDemo/loaderDemo.screen";

function App() {
  const [loading, setIsLoading] = useState(false);
  const [activeMenuItem, setMenuItem] = useState<string>("");
  const [message, setMessage] = useState<string>("");
  const [open, setIsOpen] = useState<boolean>(false);
  const [toastConfig, setConfig] = useState<any>({
    severity: "",
    message: "",
    open: false,
  });
  const [appToastConfig, setAppToastConfig] = useState<any>({
    severity: "",
    message: "",
    open: false,
  });
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  const { userInfo, isUnAuthorised, success, loginMessage } = useSelector(
    (state: any) => state.authReducer
  );
  const dispatchSessionExpired = () => dispatch<any>(sessionExpired());
  const setLoading = useCallback(
    (loading: boolean) => {
      return setIsLoading(loading);
    },
    [loading]
  );

  const loadingMemo = useMemo(
    () => ({
      setLoading,
      loading,
    }),
    [loading]
  );

  // const preferencesMemo = useMemo(
  //   () => ({
  //     setActiveMenuItem,
  //     activeMenuItem,
  //   }),
  //   [activeMenuItem]
  // );

  const setActiveMenuItem = useCallback(
    (item: string) => {
      return setMenuItem(item);
    },
    [activeMenuItem]
  );

  const getTitle = (screen: string) => {
    const title = `${"My Loco Biz"} | ${screen}`;
    return title;
  };

  useEffect(() => {
    if (userInfo && userInfo.id > 0) {
      console.log(location.pathname);

      // if(userInfo.user.isPasswordReset) {
      //   navigate("/forgotPassword");
      // }
      if (location.pathname === "/login" || location.pathname === "/") {
        navigate("/home");
      }
    } else {
      if (loginMessage) {
        setConfig({
          message: loginMessage,
          severity: ToastSeverity.Error,
          open: true,
        });
      }
      navigate("/");
    }
  }, [userInfo, success]);

  useEffect(() => {
    if (isUnAuthorised) {
      dispatchSessionExpired();
      setConfig({
        message: "Your session expired, Please login again",
        severity: ToastSeverity.Error,
        open: true,
      });
    }
  }, [isUnAuthorised]);

  const Alert = React.forwardRef<HTMLDivElement, AlertProps>(function Alert(
    props,
    ref
  ) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
  });

  const handleClose = (
    event?: React.SyntheticEvent | Event,
    reason?: string
  ) => {
    if (reason === "clickaway") {
      return;
    }

    setConfig({ ...toastConfig, open: false });
  };

  const setOpen = useCallback(
    (open: boolean) => {
      return setIsOpen(open);
    },
    [open]
  );

  const setToastMessage = useCallback(
    (message: string) => {
      return setMessage(message);
    },
    [open]
  );

  const setToastConfig = useCallback(
    (severity: ToastSeverity, message: string, open: boolean) => {
      return setConfig({ severity, message, open });
    },
    [toastConfig]
  );

  const toastMemo = useMemo(
    () => ({
      open,
      setOpen,
      message,
      setToastMessage,
      toastConfig,
      setToastConfig,
    }),
    [open, message, toastConfig]
  );

  return (
    <LoadingContext.Provider value={loadingMemo}>
      <Box className="App height100">
        {/* <PreferencesContext.Provider value={preferencesMemo}> */}
        <ToastContext.Provider value={toastMemo}>
          <Box
            className="page_loader"
            style={{ display: loadingMemo.loading ? "flex" : "none" }}
          >
            <Loader />
            {/* <ThreeCircles
              visible={true}
              height="100"
              width="100"
              color={theme.palette.primary.main}
              ariaLabel="three-circles-loading"
              wrapperStyle={{}}
              wrapperClass=""
            /> */}
          </Box>
          {/* Language Toggle Button */}

          <Routes>
            <Route path="/" element={<SignIn title={getTitle("Home")} />} />

            <Route
              path="/forgot-password"
              element={<ForgotPassword title={getTitle("Forgot Password")} />}
            />

            <Route
              path="/post-management/create-social-post"
              element={<CreateSocialPost title={getTitle("Create Post")} />}
            />

            {/* Dashboard */}
            <Route
              path="/home"
              element={<DashboardV2 title={getTitle("Dashboard")} />}
            />

            <Route
              path="/home2"
              element={<Dashboard title={getTitle("Dashboard")} />}
            />

            {/* Analytics */}
            <Route
              path="/analytics"
              element={<Analytics title={getTitle("Analytics")} />}
            />

            {/* Q&A */}
            <Route
              path="/q-and-a"
              element={<QandA title={getTitle("Q&A")} />}
            />

            {/* User Management */}
            <Route
              path="/user-management/users"
              element={<Users title={getTitle("Users")} />}
            />

            <Route
              path="/roles-management/roles"
              element={<Roles title={getTitle("Roles")} />}
            />

            {/* Business Management */}
            <Route
              path="/business-management/manage-business"
              element={<ManageBusiness title={getTitle("Manage Business")} />}
            />

            <Route
              path="/business-management/business-summary/:businessId/:accountId/:locationId"
              element={<BusinessSummary title={getTitle("Business Summary")} />}
            />

            <Route
              path="/business-management/local-business"
              element={<LocalBusiness title={getTitle("Local Business")} />}
            />

            {/* GMB Callback */}
            <Route
              path="/business-management/gmb/callback"
              element={<GMBCallback title={getTitle("Callback")} />}
            />

            {/* Review Management */}
            <Route
              path="/review-management/manage-reviews"
              element={<ManageReviews title={getTitle("Manage Reviews")} />}
            />

            {/* Post Management */}
            <Route
              path="/post-management/posts"
              element={<PostsListing title={getTitle("Posts")} />}
            />

            {/* Business Category Management */}
            <Route
              path="/business-management/categories"
              element={
                <BusinessCategoryScreen
                  title={getTitle("Business Categories")}
                />
              }
            />

            {/* Demo Screens */}
            <Route
              path="/demo/business-category"
              element={
                <DemoScreen title={getTitle("Business Category Demo")} />
              }
            />

            <Route
              path="/demo/services"
              element={
                <ServicesDemoScreen title={getTitle("Add Services Demo")} />
              }
            />

            {/* Help */}
            <Route
              path="/help"
              element={<Help title={getTitle("Manage Reviews")} />}
            />

            {/* Loader Demo */}
            <Route
              path="/loader-demo"
              element={<LoaderDemo title={getTitle("Loader Demo")} />}
            />

            {/* 404 route - MUST be last */}
            <Route
              path="*"
              element={<NotFoundPage title={getTitle("Not Found Page")} />}
            />

            <Route
              path="/un-authorized"
              element={
                <UnAuthorized title={getTitle("Un Authorized Access")} />
              }
            />

            {/* <Route
              path="/changePassword"
              element={
                <ProtectedRoute
                  allowedRoles={[
                    RoleType.SuperAdmin,
                    RoleType.Admin,
                    RoleType.User,
                    RoleType.Agent,
                  ]}
                >
                  <ChangePassword title={getTitle("Change Password")} />
                </ProtectedRoute>
              }
            /> */}
          </Routes>

          {toastConfig && (
            <Snackbar
              open={toastConfig.open}
              autoHideDuration={2000}
              onClose={handleClose}
            >
              <Alert
                onClose={handleClose}
                severity={toastConfig.severity}
                sx={{ width: "100%" }}
              >
                {toastConfig.message}
              </Alert>
            </Snackbar>
          )}
        </ToastContext.Provider>
        {/* </PreferencesContext.Provider> */}
      </Box>
    </LoadingContext.Provider>
  );
}

export default App;
