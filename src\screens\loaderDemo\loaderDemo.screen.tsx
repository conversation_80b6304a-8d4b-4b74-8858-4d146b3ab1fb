import React, { useState } from "react";
import { <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, Card, CardContent } from "@mui/material";
import Loader from "../../components/loader/loader.component";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";

interface PageProps {
  title: string;
}

const LoaderDemo: React.FC<PageProps> = ({ title }) => {
  const [showLoader, setShowLoader] = useState(false);

  const handleShowLoader = () => {
    setShowLoader(true);
    // Auto hide after 5 seconds for demo purposes
    setTimeout(() => {
      setShowLoader(false);
    }, 5000);
  };

  return (
    <LeftMenuComponent>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          LocoBiz Loader Demo
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          Simple spinning LocoBiz logo loader.
        </Typography>

        {/* Controls Section */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Test the Loader
            </Typography>
            <Box sx={{ mt: 3 }}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleShowLoader}
                disabled={showLoader}
                sx={{ mr: 2 }}
              >
                {showLoader ? "Loading Active..." : "Test Loader (5s)"}
              </Button>
              <Button
                variant="outlined"
                onClick={() => setShowLoader(false)}
                disabled={!showLoader}
              >
                Stop Loader
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* Preview Section */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Loader Preview
            </Typography>
            <Box
              sx={{
                height: 200,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "#f5f5f5",
                borderRadius: 1,
                border: "1px solid #e0e0e0",
              }}
            >
              <Loader />
            </Box>
          </CardContent>
        </Card>

        {/* CSS Information */}
        <Card sx={{ mt: 4 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              CSS Implementation
            </Typography>
            <Typography variant="body2" component="div">
              <strong>CSS Classes:</strong>
              <ul>
                <li>
                  <code>.rotating-loader-container</code> - Full screen
                  container
                </li>
                <li>
                  <code>.rotating-logo</code> - Spinning logo with 1s linear
                  rotation
                </li>
              </ul>
              <strong>Animation:</strong>
              <ul>
                <li>Simple 360° rotation every 1 second</li>
                <li>Responsive sizing (80px → 60px → 50px)</li>
                <li>Subtle drop shadow for depth</li>
              </ul>
            </Typography>
          </CardContent>
        </Card>

        {/* Full Screen Loader */}
        {showLoader && <Loader />}
      </Box>
    </LeftMenuComponent>
  );
};

export default LoaderDemo;
