import React, { useState } from "react";
import {
  <PERSON>,
  Ty<PERSON>graphy,
  Button,
  Grid,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  TextField,
} from "@mui/material";
import Loader from "../../components/loader/loader.component";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";

interface PageProps {
  title: string;
}

const LoaderDemo: React.FC<PageProps> = ({ title }) => {
  const [showLoader, setShowLoader] = useState(false);
  const [animationType, setAnimationType] = useState<"default" | "slow" | "pulse" | "bounce">("default");
  const [theme, setTheme] = useState<"light" | "dark" | "gradient">("light");
  const [showText, setShowText] = useState(true);
  const [customText, setCustomText] = useState("Loading...");

  const handleShowLoader = () => {
    setShowLoader(true);
    // Auto hide after 5 seconds for demo purposes
    setTimeout(() => {
      setShowLoader(false);
    }, 5000);
  };

  const previewConfigs = [
    { type: "default", theme: "light", label: "Default - Light" },
    { type: "slow", theme: "light", label: "Slow - Light" },
    { type: "pulse", theme: "dark", label: "Pulse - Dark" },
    { type: "bounce", theme: "gradient", label: "Bounce - Gradient" },
  ];

  return (
    <LeftMenuComponent>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          LocoBiz Loader Demo
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          Showcase of the rotating LocoBiz logo loader with different animations and themes.
        </Typography>

        {/* Controls Section */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Loader Configuration
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Animation Type</InputLabel>
                  <Select
                    value={animationType}
                    label="Animation Type"
                    onChange={(e) => setAnimationType(e.target.value as any)}
                  >
                    <MenuItem value="default">Default (2s)</MenuItem>
                    <MenuItem value="slow">Slow (3s)</MenuItem>
                    <MenuItem value="pulse">Pulse + Scale</MenuItem>
                    <MenuItem value="bounce">Bounce + Rotate</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Theme</InputLabel>
                  <Select
                    value={theme}
                    label="Theme"
                    onChange={(e) => setTheme(e.target.value as any)}
                  >
                    <MenuItem value="light">Light</MenuItem>
                    <MenuItem value="dark">Dark</MenuItem>
                    <MenuItem value="gradient">Gradient</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Loading Text"
                  value={customText}
                  onChange={(e) => setCustomText(e.target.value)}
                  disabled={!showText}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={showText}
                      onChange={(e) => setShowText(e.target.checked)}
                    />
                  }
                  label="Show Text"
                />
              </Grid>
            </Grid>
            <Box sx={{ mt: 3 }}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleShowLoader}
                disabled={showLoader}
                sx={{ mr: 2 }}
              >
                {showLoader ? "Loading Active..." : "Test Loader (5s)"}
              </Button>
              <Button
                variant="outlined"
                onClick={() => setShowLoader(false)}
                disabled={!showLoader}
              >
                Stop Loader
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* Preview Section */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Animation Previews
            </Typography>
            <Grid container spacing={3}>
              {previewConfigs.map((config, index) => (
                <Grid item xs={12} md={6} lg={3} key={index}>
                  <Card variant="outlined" sx={{ height: 300, position: "relative" }}>
                    <CardContent sx={{ textAlign: "center", p: 1 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        {config.label}
                      </Typography>
                      <Box
                        sx={{
                          height: 250,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          position: "relative",
                          backgroundColor: 
                            config.theme === "dark" ? "#1a1a1a" : 
                            config.theme === "gradient" ? "linear-gradient(135deg, #667eea 0%, #764ba2 100%)" : 
                            "#ffffff",
                          borderRadius: 1,
                        }}
                      >
                        <Loader
                          animationType={config.type as any}
                          theme={config.theme as any}
                          showText={false}
                        />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>

        {/* CSS Information */}
        <Card sx={{ mt: 4 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              CSS Classes Available
            </Typography>
            <Typography variant="body2" component="div">
              <strong>Animation Classes:</strong>
              <ul>
                <li><code>.rotating-logo</code> - Default 2s linear rotation</li>
                <li><code>.rotating-logo-slow</code> - Slower 3s linear rotation</li>
                <li><code>.rotating-logo-pulse</code> - Rotation with scale pulse effect</li>
                <li><code>.rotating-logo-bounce</code> - Rotation with bounce effect</li>
              </ul>
              <strong>Theme Classes:</strong>
              <ul>
                <li><code>.rotating-loader-container</code> - Default light theme</li>
                <li><code>.rotating-loader-container.dark-theme</code> - Dark background</li>
                <li><code>.rotating-loader-container.gradient-theme</code> - Gradient background</li>
              </ul>
            </Typography>
          </CardContent>
        </Card>

        {/* Full Screen Loader */}
        {showLoader && (
          <Loader
            animationType={animationType}
            theme={theme}
            showText={showText}
            text={customText}
          />
        )}
      </Box>
    </LeftMenuComponent>
  );
};

export default LoaderDemo;
