/* Rotating LocoBiz Logo Loader */
.rotating-loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  background-color: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
}

.rotating-logo {
  width: 80px;
  height: 80px;
  animation: rotate 2s linear infinite;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

/* Rotation animation */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Loading text animation (optional) */
.loading-text {
  position: absolute;
  bottom: 30%;
  font-family: 'Barlow', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #666666;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .rotating-logo {
    width: 60px;
    height: 60px;
  }
  
  .loading-text {
    font-size: 14px;
    bottom: 25%;
  }
}

@media (max-width: 480px) {
  .rotating-logo {
    width: 50px;
    height: 50px;
  }
  
  .loading-text {
    font-size: 12px;
    bottom: 20%;
  }
}

/* Alternative slower rotation for better UX */
.rotating-logo-slow {
  width: 80px;
  height: 80px;
  animation: rotate-slow 3s linear infinite;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

@keyframes rotate-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Pulsing rotation effect */
.rotating-logo-pulse {
  width: 80px;
  height: 80px;
  animation: rotate-pulse 2s ease-in-out infinite;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

@keyframes rotate-pulse {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.1);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

/* Bouncing rotation effect */
.rotating-logo-bounce {
  width: 80px;
  height: 80px;
  animation: rotate-bounce 2s ease-in-out infinite;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

@keyframes rotate-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: rotate(0deg) translateY(0);
  }
  40% {
    transform: rotate(180deg) translateY(-10px);
  }
  60% {
    transform: rotate(270deg) translateY(-5px);
  }
}

/* Themed background variations */
.rotating-loader-container.dark-theme {
  background-color: #1a1a1a;
}

.rotating-loader-container.dark-theme .loading-text {
  color: #ffffff;
}

.rotating-loader-container.gradient-theme {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.rotating-loader-container.gradient-theme .loading-text {
  color: #ffffff;
}
