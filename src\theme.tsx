// theme.ts
import { createTheme } from "@mui/material/styles";
import { PaletteColor, PaletteOptions, ThemeOptions } from "@mui/material";

declare module "@mui/material/styles" {
  interface Palette {
    primaryAlpha?: PaletteColor;
    secondaryAlpha?: PaletteColor;
  }

  interface PaletteOptions {
    primaryAlpha?: PaletteOptions["primary"];
    secondaryAlpha?: PaletteOptions["secondary"];
  }
}
const themeOptions: ThemeOptions = {
  palette: {
    mode: "light",
    primary: {
      main: "#49C6E5",
    },
    secondary: {
      main: "#ff6e23",
    },
    primaryAlpha: {
      main: "#2F2F2F33", // 20% opacity
    },
    secondaryAlpha: {
      main: "#ff6e2333", // 20% opacity
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          maxHeight: 30,
        },
      },
    },
  },
};

export const theme = createTheme({ ...themeOptions });
