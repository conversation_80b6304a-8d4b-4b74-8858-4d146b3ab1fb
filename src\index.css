/* @import url('https://fonts.googleapis.com/css2?family=Barlow:wght@400;500;700&display=swap'); */
/* @import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap" rel="stylesheet'); */


* {
  font-family: 'Barlow', sans-serif !important;
}


html,
body {
  margin: 0;
  font-family: '<PERSON>', sans-serif !important;
  /* -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; */
}

code {
  font-family: 'Barlow', sans-serif !important;
}

/*Page Loader Css Starts Here*/
.page_loader {
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: #21212160;
  z-index: 99999;
  justify-content: center;
  align-items: center;
}

.page_loader .loaderIcon {
  width: 50px;
  height: 50px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-top: -25px;
  margin-left: -25px;
}

:root {
  --whiteColor: #ffffff;
  --bgColor: #f4f4f4;
  /* THEME--1 */
  /* --primaryColor: #00B4E6;
  --primaryColorAlpha: #3165B133;
  --secondaryColor: #ff6e23;
  --secondaryColorAlpha: #ff6e2333; */

  /* THEME--2  */
  /* --primaryColor: #00B4E6;
  --primaryColorAlpha: #00B4E633;
  --secondaryColor: #FFC700;
  --secondaryColorAlpha: #FFC70033;  */

  /* THEME--3  */

  /* --primaryColor: #0C2A3F;
  --primaryColorAlpha:#0C2A3F33 ;
  --secondaryColor: #FF6E23;
  --secondaryColorAlpha: #FF6E2333; */

  /* THEME--4 */
  --primaryColor: #309898;
  --primaryColorAlpha: #FF9F0033;
  --secondaryColor: #F4631E;
  --secondaryColorAlpha: #F4631E33;

  --titleColor: #2F2F2F;
  --btnColor: var(--primaryColor);
  --grayBgColor: #EDEDED;
  --borderColor: #EBEBEB;
  --secondaryTextColor: #464255;
  --tableHeader: var(--grayColor);
  --grayColor: #757575;

  --teritoryColor: #464255;
  --fourthColor: #EBEBEB;
  --fourthColorText: #524E60;
  --notification: #FF5B5B;
  --positive: #089D2B;
  --negative: #CB1212;
  --positiveOpacity: #089D2B36;
  --negativeOpacity: #CB121236;
  --rating: #E7B66B;
  --ratingInactive: #757575;
  --tabHead: #F5F6FA;
  --searchBg: #FDFDFD;
  --reviewDescription: #0D0C22;
  --delete: #F24245;
}

html,
body {
  height: 100%;
  /* overflow-y: scroll; */
  overflow-y: auto;
}

body {
  background-color: var(--bgColor) !important;
}

.height100 {
  height: 100%;
}

.width100 {
  width: 100%;
}

.navFullLogo {
  width: 75%;
}

.navIcon {
  text-align: center;
}

.navIcon img {
  width: 70%;
}

.navLogoPart {
  height: 120px;
  margin-top: 20px;
  margin-bottom: 16px;
}

.floatR {
  float: right;
}

.columnEnd {
  display: flex;
  justify-content: end;
}

.lh42 {
  line-height: 42px;
}

.pad0 {
  padding: 0px !important;
}


.whiteBg {
  background-color: var(--whiteColor);
}

.tabBg {
  background-color: var(--tabHead);
  border: 1px solid #d5d5d5;
  border-radius: 14px 14px 0px 0px;
}

.commonFormPart {
  background-color: var(--whiteColor);
  padding: 16px;
}

.commonTabs {
  border-radius: 14px;
  box-shadow: 6px 6px 54px rgba(0, 0, 0, 0.05);
}

.commonTabsIcon {
  width: 50px;
  height: 50px;
  margin: 0px 0px 12px 0px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  background-color: var(--primaryColor);
  color: var(--whiteColor);
}

.commonTabs .Mui-selected {
  background-color: var(--secondaryColor);
  color: var(--whiteColor);
  border-radius: 8px !important;
  margin: 7px 0px;
  border: 0px !important;
  font-weight: 600;
  text-transform: capitalize;
}

.primaryFillBtn {
  background-color: var(--btnColor) !important;
  box-shadow: none !important;
  padding: 17px 16px !important;
  border-radius: 5px !important;
  text-transform: capitalize !important;
  font-size: 16px !important;
}

.secondaryOutlineBtn {
  background-color: var(--whiteColor) !important;
  border: 1px solid var(--borderColor) !important;
  color: var(--secondaryTextColor) !important;
  padding: 16px !important;
  border-radius: 5px !important;
  text-transform: capitalize;
}

.secondaryFillBtn {
  background-color: var(--secondaryColor);
}

.commonCard {
  background-color: var(--whiteColor) !important;
  border-radius: 4px !important;
  padding: 24px;
  /* box-shadow: 0px 4px 4px -4px rgba(0, 0, 0, 0.75);
  -webkit-box-shadow: 0px 4px 4px -4px rgba(0, 0, 0, 0.75);
  -moz-box-shadow: 0px 4px 4px -4px rgba(0, 0, 0, 0.75); */
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.05);
  border-radius: 5px !important;
  border: 0px;
}

.commonCardDescription {
  padding-bottom: 20px;
}

.pageTitle {
  font-size: 32px !important;
  font-weight: 600 !important;
  color: var(--titleColor);
  display: flex;
  align-items: center;
  margin: 0px;
}

.dashboardCard {
  display: flex;
}

.d-flex {
  display: flex;
}

.dashboardCard .dashboardIcon {
  width: 85px;
}

/*Common Topbar Css*/
.commonTopBar {
  display: flex;
  box-shadow: none !important;
  color: var(--titleColor) !important;
}

.commonTopBarR {
  display: flex;
  /* justify-content: end;
  flex-grow: 1;
  align-items: center; */
  position: absolute;
  z-index: 9;
  right: 0px;
  width: 100%;
  justify-content: end;
  padding: 10px 12px;
  background-color: #f4f4f4;
}

.notification {
  position: relative;
  margin-right: 32px;
}

.notificationIcon {
  width: 40px;
  height: 40px;
  background-color: #ffffff6e;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--notification);
  border-radius: 12px;
}

.notificationIcon .iconBtn {
  color: var(--bgColor) !important;
}

.notificationValue {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
  color: var(--whiteColor);
  background-color: var(--notification);
  position: absolute;
  top: -8px;
  right: -8px;
  border: 2px solid #ffffff;
  font-size: 12px;
}

.profileName {
  padding: 12px 12px 12px 18px;
  border-right: 1px solid var(--grayColor);
  color: var(--titleColor);
}

.profileName .userName {
  font-weight: 600;
}

.profilePic {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 2px solid var(--whiteColor);
}

.profilePic {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 2px solid var(--whiteColor);
}

/*Status Css*/
.success {
  color: var(--positive);
}

.cancelled {
  color: var(--negative);
}

/*Status Css*/
/*Badges Css*/
.badgeTextAdmin {
  background-color: var(--primaryColor);
  color: var(--whiteColor);
  font-size: 13px !important;
  font-weight: 300 !important;
  width: max-content;
  padding: 0px 18px;
  border-radius: 3px;
}

.badgeTextUser {
  background-color: var(--secondaryColor);
  color: var(--whiteColor);
  font-size: 13px !important;
  font-weight: 300 !important;
  width: max-content;
  padding: 0px 18px;
  border-radius: 3px;
}

.badgeTextManager {
  background-color: var(--positive);
  color: var(--whiteColor);
  font-size: 13px !important;
  font-weight: 300 !important;
  width: max-content;
  padding: 0px 18px;
  border-radius: 3px;
}

.labelTag {
  position: absolute;
  top: 39px;
  right: -45px;
  background-color: var(--secondaryColor);
  color: var(--whiteColor);
  font-weight: 500;
  transform: rotate(45deg);
  width: 200px;
  text-align: center;
  padding: 0px;
  font-size: 14px;
}

/*Badges Css*/
.selected {
  background-color: var(--positive) !important;
  color: var(--whiteColor) !important;
}

/*Form Fields Css*/
.commonInput {
  margin-bottom: 20px;
}

.commonInput .MuiFilledInput-root {
  border-radius: 8px !important;
  height: 58px;
  background-color: transparent;
}

.searchInput {
  height: 40px;
  background-color: var(--searchBg);
  border-radius: 5px;
  display: flex;
  align-items: center;
  padding-right: 12px;
  max-width: 300px;
}

.placeHolderIcon {
  color: var(--grayColor);
}

.searchInput .MuiFilledInput-root {
  background-color: transparent;
}

.commonSelect {
  /* background-color: #ffffff; */
}

.hightlightBoxText {
  display: flex;
  align-items: center;
}

.hightlightBox {
  display: block;
  width: 12px;
  height: 12px;
  border-radius: 3px;
  background-color: var(--grayColor);
  float: left;
  margin-right: 8px;
}

.hightlightBox.admin {
  background-color: var(--primaryColor);
}

.hightlightBox.manager {
  background-color: var(--positive);
}

.hightlightBox.user {
  background-color: var(--secondaryColor);
}

/*Table Css Starts Here*/
.commonTableHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0px;
}

.commonTableHeader .commonSelect {
  max-width: 240px;
}

.commonTableActionBtns {
  display: flex;
}

.commonTable table {
  border: 1px solid #ccc;
  width: 100%;
  margin: 0;
  padding: 0;
  border-collapse: collapse;
  border-spacing: 0;
}

.commonTable table tr {
  border: 1px solid #ddd;
  padding: 5px;
}

.commonTable table th,
.commonTable table td {
  padding: 10px;
  text-align: left;
}

.commonTable {
  overflow: auto;
  background-color: var(--whiteColor);
  -webkit-box-shadow: 0px 4px 5px 0px rgba(204, 204, 204, 1);
  -moz-box-shadow: 0px 4px 5px 0px rgba(204, 204, 204, 1);
  box-shadow: 0px 4px 5px 0px rgba(204, 204, 204, 1);
  border-radius: 8px;
  border: 0px;
}

.commonTable thead th {
  position: sticky;
  top: 0;
  z-index: 1;
  color: var(--whiteColor);
  background-color: var(--tableHeader);
  font-weight: 600;
}

.commonTable table {
  border-collapse: collapse;
  width: 100%;
}

.commonTable th,
.commonTable td {
  padding: 8px 16px;
}

.commonTable th {
  background: #eee;
}

.emptyBtn {
  box-shadow: none;
  border: 0px !important;
  width: 30px;
  min-width: 30px;
  min-height: 30px;
  margin-right: 18px;
}

.emptyBtn:hover {
  border-radius: 30px !important;
}

.emptyBtn .MuiButton-startIcon {
  margin-right: 0px;
  margin-left: 0px;
}

.tablePrimaryText {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: var(--secondaryTextColor);
}

.tableSecondaryText {
  font-size: 13px !important;
  font-weight: 400 !important;
  color: var(--grayColor);
}

.tableActionBtn {
  background-color: var(--secondaryColor) !important;
  color: var(--whiteColor) !important;
  height: 40px;
  min-height: 40px;
  padding: 8px 24px;
  line-height: 40px;
  border-radius: 5px !important;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 18px;
  border: 1px solid var(--borderColor);
}

.tableActionBtnDisabled {
  background-color: var(--secondaryColorAlpha) !important;
  color: var(--whiteColor) !important;
  height: 40px;
  min-height: 40px;
  padding: 8px 24px;
  line-height: 40px;
  border-radius: 5px !important;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 18px;
  border: 1px solid var(--borderColor);
}

.tableUserInfo {
  display: flex;
}

.tableUserInfo img {
  padding-right: 12px;
}

@media screen and (max-width: 600px) {
  .commonTable table {
    border: 0;
    background-color: var(--bgColor);
  }

  .commonTable table thead {
    display: none;
  }

  .commonTable table tr {
    margin-bottom: 10px;
    display: block;
    border-bottom: 2px solid #ddd;
    background-color: var(--whiteColor);
  }

  .commonTable table td {
    display: block;
    text-align: right;
    font-size: 13px;
    border-bottom: 1px dotted #ccc;
  }

  .commonTable table td:last-child {
    border-bottom: 0;
  }

  .commonTable table td:before {
    content: attr(data-label);
    float: left;
    text-transform: uppercase;
    font-weight: bold;
  }
}

.gridResponsiveTextLeft {
  text-align: left !important;
}

.errorMessage {
  color: #f00 !important;
  font-weight: 400 !important;
}

/*Responsive Table Css Ends Here*/
.replyBtn {
  background-color: var(--primaryColor) !important;
  text-transform: capitalize;
}

.postBtn {
  background-color: var(--teritoryColor) !important;
  border: 2px solid var(--fourthColor);
  border-radius: 5px !important;
  box-shadow: none;
  text-transform: capitalize;
}

.button-border-radius {
  border-radius: 4px !important;
}

.text-box-border-radius {
  border-radius: 4px !important;
  border-bottom-right-radius: 4px !important;
  border-bottom-left-radius: 4px !important;
}


.tagBtn {
  background-color: var(--fourthColor) !important;
  color: #524E60 !important;
  box-shadow: none !important;
  border-radius: 5px !important;
  text-transform: capitalize;
  font-weight: 600;
}

.reviewDate {
  color: var(--fourthColorText) !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  text-align: right;
}

.tagChips {
  margin-right: 12px !important;
}

.chipTagCount {
  padding: 0px 4px;
}

.display-contents {
  display: contents;
}

/*MUI OverWritten Css Starts Here*/
/* .MuiAppBar-root
{
  z-index: -1 !important;
} */
/*MUI OverWritten Css Ends Here*/
.headerMenu {
  display: flex;
  justify-content: space-between;
}

.commonDrawer {
  z-index: 9999;
}

/*Common Modal Css Starts Here*/
.commonModal {
  height: 100%;
}

.commonModal .modal-modal-title {
  padding: 8px 12px;
  background-color: #EDEDED;
}

.commonModal .modal-modal-description {
  padding: 24px 12px;
  height: calc(100% - 108px);
  overflow-y: visible;
}

.commonModal .commonTitle {
  color: var(--titleColor);
  font-weight: 600 !important;
  font-size: 32px !important;
}

.commonModal .modal-sub-title {
  font-size: 12px;
  padding-left: 6px;
}

.commonModal .commonFooter {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #EBEBEB;
  padding: 12px 12px;
}

.createpost.createpost .modal-modal-description {
  padding: 0px;
  overflow: visible;
}

.createpost .primaryFillBtn {
  font-size: 16px !important;
  padding: 18px 16px !important;
  font-weight: 500;
}

.commonLabel {
  font-size: 12px;
  color: var(--grayColor);
}

.managementPostCard .commonLabel {
  font-size: 12px;
  color: var(--grayColor);
  padding-top: 12px;
  font-weight: 600;
}

.managementPostCard .commonInput {
  font-size: 12px;
  color: var(--titleColor);
  padding-top: 12px;
}

.managementPostCard .paddingLeft {
  padding-left: 16px;
}

/*Common Modal Css Ends Here*/

/*Custom Scroll Bar Css Starts Here*/
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px #ebebeb;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #757575;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #757575;
}

/*Custom Scroll Bar Css Ends Here*/
.marT0 {
  margin-top: 0px !important;
}

.marB20 {
  margin-bottom: 20px;
}

.fs20 {
  font-size: 20px;
}

button {
  border-radius: 0 !important;
}

.minified-content {
  display: -webkit-box;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 9em;
  line-height: 1.5em;
  word-wrap: break-word;
  text-align: justify;
}

/*Create Post Css*/
.locationCreatePost {
  padding: 16px 20px;
  height: 100%;
}

.lceLeft,
.lceRight {
  padding: 24px;
  height: 100%;
  overflow-y: visible;
}

.errorCard .messageText {
  color: var(--negative) !important;
  font-weight: 600;

}

.successCard .messageText {
  color: var(--positive) !important;
  font-weight: 600;
}

.messageTextMissing {
  padding: 5px;
  border-radius: 5px;
  font-size: 15px;
  background-color: var(--negativeOpacity);
  color: var(--negative);
}

.missingStatusCount {
  width: 30px;
  height: 30px;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
}

.errorCard .missingStatusCount {
  background-color: var(--negativeOpacity);
  color: var(--negative);
}

.successCard .missingStatusCount {
  background-color: var(--positiveOpacity);
  color: var(--positive);
}

.m-0 {
  margin: 0px;
}

.textTruncate {
  width: 100%;
  height: 100px;
  max-height: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  word-break: break-word;
}

.not-allowed {
  cursor: not-allowed;
}

.pagination-Text {
  padding-left: 10px;
  font-family: 'Barlow', sans-serif !important;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  display: table-cell;
  text-align: left;
  color: rgba(0, 0, 0, 0.87);
}

.cardIcon {
  width: 38px;
  height: 38px;
  display: flex;
  margin-right: 12px;
  justify-content: center;
  align-items: center;
  border-radius: 38px;
}

.cardIcon svg {
  font-size: 20px;
}


.greenBgCcolor {
  background-color: #1ABC9C1a;
  color: #1ABC9C;
}

.violetBgCcolor {
  background-color: #8E44AD1a;
  color: #8E44AD;
}

.darkBgCcolor {
  background-color: #2C3E501a;
  color: #2C3E50;
}

.orangeBgCcolor {
  background-color: #F39C121a;
  color: #F39C12;
}

.yellowBgCcolor {
  background-color: #F1C40F1a;
  color: #F1C40F;
}

.cardTitle {
  font-size: 16px;
  font-weight: 600;
}

.cardTitleIcon {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.cardCount {
  font-size: 16px;
  font-weight: 700;
  display: flex;
  justify-content: end;
  flex-grow: 1;
  align-items: center;
}

.commonCardHead {
  display: flex;
  flex-direction: row;
  padding-bottom: 16px;
}

.selectedMenu,
.selectedMenu:hover {
  background-color: var(--secondaryColorAlpha);
  color: var(--secondaryColor);
  font-weight: 600;
}

.selectedMenu .selectedIcon {
  /* background-color: var(--secondaryColorAlpha); */
  background-color: transparent;
  color: var(--secondaryColor);
  font-weight: 600;
}

.selectedMenu {
  margin-left: 12px;
}

.selectedMenu::before {
  content: "";
  position: absolute;
  width: 6px;
  background-color: var(--secondaryColor);
  height: 48px;
  left: -12px;
}

.commonCardBottomSpacing {
  margin-bottom: 24px;
}

.subtitle2 {
  font-size: 18px !important;
  color: #A3A3A3 !important;
  font-weight: 500 !important;
  margin-bottom: 24px;
}

.commonVerticalCenter {
  display: flex;
  align-items: center;
}

.commonShapeBtn {
  box-shadow: none;
  border-radius: 5px !important;
  text-transform: capitalize;
  font-size: 16px;
  min-height: 55px !important;
}

.updatesShapeBtn {
  box-shadow: none;
  border-radius: 5px !important;
  text-transform: capitalize;
  font-size: 14px;
  min-height: 35px !important;
}


.chipsLabel {
  line-height: 32px;
  font-weight: 600;
}

.commonChipBtn {
  background-color: #ffffff;
  border: 2px solid var(--fourthColor);
}

.marT30 {
  margin-top: 30px;
}

.titleIcon {
  display: flex;
  padding-bottom: 12px;
}

.titleIcon span {
  display: flex;
  align-items: center;
  margin-right: 6px;
}

.padL32 {
  padding-left: 32px;
}

.editIconBtn {
  color: var(--secondaryColor) !important;
}

.viewIconBtn {
  color: var(--primaryColor) !important;
}

.editIconBtnDisabled {
  color: var(--secondaryColorAlpha) !important;
}

.deleteIconBtn {
  color: var(--delete) !important;
}

.deleteIconBtnDisabled {
  color: var(--negativeOpacity) !important;
}

.mar0 {
  margin: 0px;
}

.sampleBusineesMobile {
  height: 490px;
}

.sampleBusineesMobile img {
  width: 97%;
  height: 97%;
  object-fit: contain;
}

.sectionTitle {
  font-size: 28px;
  font-weight: 500;
}

.commonBorderCard {
  box-shadow: none;
  border: 1px solid rgba(0, 0, 0, 0.12);
}

.boxShadowNone {
  box-shadow: none;
}

.shadowCase {
  box-shadow: none;
}

.shadowCase:hover {
  box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
}

.customColSpacing {
  padding: 1rem 0 0 1rem !important;
}

.responsiveShow {
  display: none;
}

/*Responsive Css Starts Here*/
@media (max-width: 768px) {
  .pageTitle {
    font-size: 20px !important;
  }

  .responsiveHide {
    display: none;
  }

  .responsiveShow {
    display: block;
  }

  .commonTableActionBtns {
    justify-content: flex-end;
  }

  .stackResponsive {
    white-space: normal;
    flex-wrap: wrap;
  }

  .stackResponsiveColumn {
    display: flex;
    flex-direction: column;
  }

  .stackResponsiveColumnRight {
    margin: 12px 0px !important;
  }

  .commonSelect {
    width: 180px;
    min-width: 100%;
  }

  main {
    padding: 16px !important;
  }

  .quickSortChips .commonChipBtn {
    width: 52px;
    height: 52px;
    border-radius: 42px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .quickSortChips .commonChipBtn svg {
    position: relative;
    left: 5px;
  }

  .responsivePostModal {
    height: auto;
    overflow-y: visible;
  }

  .responsivePostModalRight {
    height: 0px;
  }

  .responsivePostModalRight .postPreview {
    padding: 16px;
    max-width: 100%;
  }

  .commonTabs {
    width: 100px;
    max-width: 100%;
    min-width: 100%;
  }


  /*Default Overide Css Starts Here*/
  .css-dm569u-MuiButton-startIcon {
    margin-right: 0px;
  }
}

/*Responsive Css Ends Here*/