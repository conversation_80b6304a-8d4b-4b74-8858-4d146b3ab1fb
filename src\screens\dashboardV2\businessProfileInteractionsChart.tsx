import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Tooltip,
  Legend,
} from "chart.js";
import { Box, Typography } from "@mui/material";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { theme } from "../../theme";

ChartJS.register(
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Tooltip,
  Legend
);

interface DatedValue {
  date: { year: number; month: number; day: number };
  value?: string;
}

interface TimeSeries {
  dailyMetric: string;
  timeSeries: { datedValues: DatedValue[] };
}

const BusinessProfileInteractionsChart = ({
  rawData,
  isSameMonthYear,
}: {
  rawData: any;
  isSameMonthYear: boolean;
}) => {
  const [total, setTotal] = useState<number>(0);

  const transformMonthlyData = (metrics: TimeSeries[]) => {
    const includedMetrics = [
      "CALL_CLICKS",
      "BUSINESS_DIRECTION_REQUESTS",
      "WEBSITE_CLICKS",
      "BUSINESS_CONVERSATIONS",
    ];
    const monthMap: Record<string, number> = {};
    const dailyMap: Record<string, number> = {};

    metrics.forEach((metric) => {
      if (includedMetrics.includes(metric.dailyMetric)) {
        metric.timeSeries.datedValues.forEach(({ date, value }) => {
          const key = isSameMonthYear
            ? dayjs(`${date.year}-${date.month}-${date.day}`).format(
                "YYYY-MMM-DD"
              )
            : dayjs(`${date.year}-${date.month}-01`).format("MMM YYYY");
          const numericValue = parseInt(value || "0", 10);
          if (isSameMonthYear) {
            dailyMap[key] = (dailyMap[key] || 0) + numericValue;
          } else {
            monthMap[key] = (monthMap[key] || 0) + numericValue;
          }
        });
      }
    });

    const labels = isSameMonthYear
      ? Object.keys(dailyMap).sort()
      : Object.keys(monthMap).sort(
          (a, b) => dayjs(a, "MMM YYYY").unix() - dayjs(b, "MMM YYYY").unix()
        );
    const data = isSameMonthYear
      ? labels.map((daily) => dailyMap[daily])
      : labels.map((month) => monthMap[month]);

    return { labels, data };
  };

  const { labels, data } = transformMonthlyData(
    rawData.multiDailyMetricTimeSeries[0].dailyMetricTimeSeries
  );

  // Use useEffect to setTotal outside render flow
  useEffect(() => {
    const sum = data.reduce((acc, val) => acc + val, 0);
    setTotal(sum);
  }, [data]);

  const chartData = {
    labels,
    datasets: [
      {
        label: "Interactions",
        data,
        fill: true,
        tension: 0,
        backgroundColor: theme.palette.secondaryAlpha?.main,
        borderColor: theme.palette.secondary?.main,
        pointBackgroundColor: theme.palette.secondary?.main,
        pointBorderColor: theme.palette.secondary?.main,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: { display: false },
      tooltip: { mode: "index" as const, intersect: false },
    },
    scales: {
      x: { grid: { display: false } },
      y: { beginAtZero: true },
    },
  };

  return (
    <Box sx={{ p: 2, backgroundColor: "#fff", borderRadius: 2 }}>
      <Box sx={{ flexGrow: 1 }}>
        <Box sx={{ p: 3, borderRadius: 2 }}>
          <Typography variant="h5" fontWeight="bold" gutterBottom>
            {"Business Profile interactions"}
          </Typography>
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            {total}
          </Typography>
          <Typography variant="subtitle2" sx={{ mb: 2 }}>
            {
              "Interactions are when people call, message, make bookings, ask for directions and more from your Business Profile on Google"
            }
          </Typography>
          <Line data={chartData} options={chartOptions} height={90} />
        </Box>
      </Box>
    </Box>
  );
};

export default BusinessProfileInteractionsChart;
